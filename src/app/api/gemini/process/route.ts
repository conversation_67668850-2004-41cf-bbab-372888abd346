import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { readFile, writeFile } from 'fs/promises';
import { join } from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '');

// Load company settings
async function loadCompanySettings() {
  try {
    const uploadsDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'uploads') : join(process.cwd(), 'uploads');
    const settingsPath = join(uploadsDir, 'settings.json');
    const settingsData = await readFile(settingsPath, 'utf-8');
    const settings = JSON.parse(settingsData);
    return settings.companyInfo;
  } catch (error) {
    // Return default settings if file doesn't exist
    return {
      companyName: 'QuantumRhino',
      contactName: '<PERSON>',
      email: '<EMAIL>',
      phone: '+****************',
      address: '123 Innovation Drive, Tech City, TC 12345',
      website: 'https://quantumrhino.com'
    };
  }
}

// Validate structure match between original and generated content
function validateStructureMatch(original: string, generated: string): { isMatch: boolean; issues: string[] } {
  const issues: string[] = [];

  // Extract headers from both
  const originalHeaders = original.match(/^#{1,6}\s+.+$/gm) || [];
  const generatedHeaders = generated.match(/^#{1,6}\s+.+$/gm) || [];

  // Only check for major header discrepancies (more than 30% difference)
  const headerCountDiff = Math.abs(originalHeaders.length - generatedHeaders.length);
  const headerCountThreshold = Math.max(3, originalHeaders.length * 0.3);

  if (headerCountDiff > headerCountThreshold) {
    issues.push(`Significant header count mismatch: original has ${originalHeaders.length}, generated has ${generatedHeaders.length}`);
  }

  // Extract lists from both (simplified validation)
  const originalLists = original.match(/^[\s]*[-*+]\s+.+$/gm) || [];
  const generatedLists = generated.match(/^[\s]*[-*+]\s+.+$/gm) || [];

  // Only check if there's a major discrepancy (more than 50% difference)
  const listCountDiff = Math.abs(originalLists.length - generatedLists.length);
  const listCountThreshold = Math.max(5, originalLists.length * 0.5);

  if (listCountDiff > listCountThreshold) {
    issues.push(`Significant list item count mismatch: original has ${originalLists.length}, generated has ${generatedLists.length}`);
  }

  return {
    isMatch: issues.length === 0,
    issues
  };
}

// Validate and preprocess user input
function validateAndPreprocessInput(prompt: string): { isValid: boolean; processedPrompt: string; warnings: string[] } {
  const warnings: string[] = [];
  let processedPrompt = prompt.trim();

  // Check for minimum information
  const hasClientInfo = /client|company|customer/i.test(processedPrompt);
  const hasProjectInfo = /project|work|task|service/i.test(processedPrompt);
  const hasBudgetInfo = /budget|cost|price|\$|dollar|fee/i.test(processedPrompt);
  const hasTimelineInfo = /timeline|deadline|date|week|month|schedule/i.test(processedPrompt);

  if (!hasClientInfo) {
    warnings.push('No client information detected - will use placeholders');
  }
  if (!hasProjectInfo) {
    warnings.push('No project details detected - will use generic descriptions');
  }
  if (!hasBudgetInfo) {
    warnings.push('No budget information detected - will use placeholder amounts');
  }
  if (!hasTimelineInfo) {
    warnings.push('No timeline information detected - will use placeholder dates');
  }

  // Enhance prompt with missing information guidance
  if (warnings.length > 0) {
    processedPrompt += '\n\nNOTE: Some information appears to be missing. Please use professional placeholders for any missing details and indicate what information needs to be provided later.';
  }

  return {
    isValid: true, // Always valid - just provide warnings
    processedPrompt,
    warnings
  };
}

// Create intelligent fallback when Gemini fails
function createIntelligentFallback(originalMarkdown: string, userPrompt: string, companyInfo: any): string {
  console.log('🔄 FALLBACK: Creating intelligent fallback content...');

  let fallbackContent = originalMarkdown;

  // Extract key information from user prompt
  const clientMatch = userPrompt.match(/client[:\s]+([^,.\n]+)/i);
  const projectMatch = userPrompt.match(/project[:\s]+([^,.\n]+)/i);
  const budgetMatch = userPrompt.match(/budget[:\s]*\$?([0-9,]+)/i);
  const timelineMatch = userPrompt.match(/timeline[:\s]+([^,.\n]+)/i);

  // Basic replacements
  const replacements = [
    // Company information
    { pattern: /\[COMPANY[_\s]*NAME\]/gi, value: companyInfo.companyName },
    { pattern: /\[CONTACT[_\s]*NAME\]/gi, value: companyInfo.contactName },
    { pattern: /\[EMAIL\]/gi, value: companyInfo.email },
    { pattern: /\[PHONE\]/gi, value: companyInfo.phone },
    { pattern: /\[ADDRESS\]/gi, value: companyInfo.address },
    { pattern: /\[WEBSITE\]/gi, value: companyInfo.website },

    // Client information
    { pattern: /\[CLIENT[_\s]*NAME\]/gi, value: clientMatch ? clientMatch[1].trim() : '[CLIENT NAME TO BE PROVIDED]' },
    { pattern: /\[PROJECT[_\s]*NAME\]/gi, value: projectMatch ? projectMatch[1].trim() : '[PROJECT NAME TO BE PROVIDED]' },
    { pattern: /\[BUDGET\]/gi, value: budgetMatch ? `$${budgetMatch[1]}` : '[BUDGET TO BE DETERMINED]' },
    { pattern: /\[TIMELINE\]/gi, value: timelineMatch ? timelineMatch[1].trim() : '[TIMELINE TO BE DETERMINED]' },

    // Dates
    { pattern: /\[START[_\s]*DATE\]/gi, value: '[START DATE TO BE DETERMINED]' },
    { pattern: /\[END[_\s]*DATE\]/gi, value: '[END DATE TO BE DETERMINED]' },
    { pattern: /\[DATE\]/gi, value: new Date().toLocaleDateString() },
  ];

  // Apply replacements
  replacements.forEach(({ pattern, value }) => {
    fallbackContent = fallbackContent.replace(pattern, value);
  });

  // Add fallback notice
  fallbackContent += '\n\n---\n*Note: This SOW was generated using fallback processing. Please review and update any placeholder information marked with brackets.*';

  console.log('✅ FALLBACK: Intelligent fallback content created');
  return fallbackContent;
}

// Validate template structure and content
async function validateTemplateStructure(markdown: string, templateId: string): Promise<{ isValid: boolean; issues: string[]; suggestions: string[] }> {
  const issues: string[] = [];
  const suggestions: string[] = [];

  // Check for basic SOW elements
  const hasHeaders = /^#{1,6}\s+/m.test(markdown);
  const hasSOWKeywords = /statement\s+of\s+work|scope|deliverable|timeline|budget|cost/i.test(markdown);
  const hasPlaceholders = /\[.*\]|\{.*\}|_____|XXXX/i.test(markdown);
  const hasContactInfo = /email|phone|address|contact/i.test(markdown);

  if (!hasHeaders) {
    issues.push('No clear document structure detected (missing headers)');
    suggestions.push('Will add standard SOW section headers');
  }

  if (!hasSOWKeywords) {
    issues.push('Document does not appear to be SOW-related');
    suggestions.push('Will adapt content to SOW format while preserving structure');
  }

  if (!hasPlaceholders) {
    issues.push('No placeholders detected for dynamic content');
    suggestions.push('Will identify areas that need client-specific information');
  }

  if (!hasContactInfo) {
    issues.push('No contact information fields detected');
    suggestions.push('Will add contact information sections');
  }

  // Check document length
  if (markdown.length < 500) {
    issues.push('Template appears to be very short');
    suggestions.push('Will enhance with standard SOW content');
  }

  if (markdown.length > 50000) {
    issues.push('Template is very large - may affect processing');
    suggestions.push('Will focus on key sections for updates');
  }

  return {
    isValid: true, // Always valid - just provide feedback
    issues,
    suggestions
  };
}

// Get original DOCX structure for comparison (FIXED: Handle saved templates)
async function getOriginalDocxStructure(templateId: string): Promise<string> {
  try {
    console.log('🔍 STRUCTURE EXTRACTION: Getting original DOCX structure for template:', templateId);

    const storageRoot = process.env.STORAGE_PATH || join(process.cwd(), 'storage');

    // FIRST: Check saved templates system (same as preview API fix)
    const savedTemplatesPath = join(storageRoot, 'saved-templates.json');

    try {
      const savedTemplatesData = await readFile(savedTemplatesPath, 'utf-8');
      const savedTemplates = JSON.parse(savedTemplatesData);
      const savedTemplate = savedTemplates.find((t: any) => t.id === templateId);

      if (savedTemplate) {
        console.log('✅ STRUCTURE EXTRACTION: Found saved template, returning markdown structure');
        console.log('📊 STRUCTURE EXTRACTION: Saved template markdown length:', savedTemplate.markdown.length);
        return savedTemplate.markdown; // Return the original markdown structure
      }
    } catch (savedError) {
      console.log('📝 STRUCTURE EXTRACTION: No saved templates found, checking uploaded files...');
    }

    // SECOND: Check uploads directory for uploaded DOCX files
    const uploadsDir = join(storageRoot, 'uploads');
    const originalDocxPath = join(uploadsDir, `${templateId}.docx`);

    try {
      // Check if DOCX file exists
      await readFile(originalDocxPath);
      console.log('✅ STRUCTURE EXTRACTION: Found uploaded DOCX file');

      // Convert original DOCX to markdown to get exact structure with content control preservation
      const tempStructureMd = join(uploadsDir, `structure-${templateId}-${Date.now()}.md`);
      const pandocCommand = `pandoc "${originalDocxPath}" -f docx -t markdown --wrap=none --preserve-tabs --extract-media=./temp-media -o "${tempStructureMd}"`;

      console.log('🔍 STRUCTURE EXTRACTION: Running pandoc command:', pandocCommand);
      await execAsync(pandocCommand);

      const originalStructure = await readFile(tempStructureMd, 'utf-8');
      console.log('✅ STRUCTURE EXTRACTION: Original structure extracted, length:', originalStructure.length);

      // Clean up temp file
      try {
        await execAsync(`rm "${tempStructureMd}"`);
        await execAsync(`rm -rf ./temp-media`); // Clean up extracted media
      } catch (cleanupError) {
        console.warn('Cleanup warning:', cleanupError);
      }

      return originalStructure;
    } catch (docxError) {
      console.log('📝 STRUCTURE EXTRACTION: No uploaded DOCX found, checking templates directory...');
    }

    // THIRD: Check templates directory (user-specific storage)
    const templatesDir = join(storageRoot, 'templates');
    try {
      const { readdir } = require('fs/promises');
      const userDirs = await readdir(templatesDir).catch(() => null);

      if (userDirs) {
        // This would require user context - for now, skip this fallback
        console.log('📝 STRUCTURE EXTRACTION: Templates directory exists but requires user context');
      }
    } catch (templatesError) {
      console.log('📝 STRUCTURE EXTRACTION: Templates directory not accessible');
    }

    throw new Error(`Template not found in any storage location: ${templateId}`);

  } catch (error) {
    console.error('❌ STRUCTURE EXTRACTION: Failed to extract original structure:', error);
    throw new Error('Failed to extract original DOCX structure');
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('🎯 GEMINI SELF-VALIDATION: Starting enhanced processing...');

    const { markdown, prompt, templateId } = await request.json();
    console.log('Request data received - markdown length:', markdown?.length, 'prompt length:', prompt?.length, 'templateId:', templateId);

    if (!markdown || !prompt || !templateId) {
      console.log('Missing required data - markdown:', !!markdown, 'prompt:', !!prompt, 'templateId:', !!templateId);
      return NextResponse.json({ error: 'Missing markdown, prompt, or templateId' }, { status: 400 });
    }

    // Validate and preprocess user input (non-blocking)
    console.log('🔍 VALIDATION: Analyzing user input...');
    const inputValidation = validateAndPreprocessInput(prompt);

    if (inputValidation.warnings.length > 0) {
      console.log('⚠️ VALIDATION: Input warnings:', inputValidation.warnings);
    }

    // Use the processed prompt (but don't block on validation failures)
    const processedPrompt = inputValidation.processedPrompt;

    // Load company settings and original structure
    const companyInfo = await loadCompanySettings();
    console.log('Loaded company info:', companyInfo);

    // Get original DOCX structure for comparison
    const originalStructure = await getOriginalDocxStructure(templateId);
    console.log('✅ Original DOCX structure extracted for comparison');

    // Validate template structure (non-blocking)
    console.log('🔍 VALIDATION: Analyzing template structure...');
    const templateValidation = await validateTemplateStructure(markdown, templateId);
    if (templateValidation.issues.length > 0) {
      console.log('⚠️ VALIDATION: Template issues detected:', templateValidation.issues);
      console.log('💡 VALIDATION: Applying suggestions:', templateValidation.suggestions);
    }

    // Check if we're in development mode with mock key
    console.log('Gemini API Key present:', !!process.env.GEMINI_API_KEY);
    console.log('API Key starts with:', process.env.GEMINI_API_KEY?.substring(0, 10) + '...');

    if (!process.env.GEMINI_API_KEY || process.env.GEMINI_API_KEY === 'YOUR_ACTUAL_GEMINI_API_KEY_HERE') {
      // Mock response for development
      console.log('Using mock Gemini API response for development');

      return NextResponse.json({
        updatedMarkdown: `${markdown}\n\n<!-- Mock Mode: Prompt received: ${prompt.substring(0, 100)}... -->`,
        message: 'SOW updated successfully with prompt information (Mock Mode)'
      });
    }

    // Get the generative model
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

    // Create structure-preserving content replacement prompt
    const geminiPrompt = `
You are a PRECISION DOCUMENT STRUCTURE PRESERVATIONIST. Your PRIMARY job is to maintain EXACT document structure while intelligently replacing content.

🏗️ **STRUCTURE PRESERVATION IS PARAMOUNT:**

1. **PRESERVE EXACT DOCUMENT ARCHITECTURE:** Maintain every structural element exactly as shown
2. **CONTENT CONTROL ANCHOR INTEGRITY:** Never break field boundaries, table cells, or content control markers
3. **TABLE STRUCTURE SANCTITY:** Every pipe (|), dash (-), and cell boundary must remain identical
4. **FORMATTING FIDELITY:** Bold, italic, spacing, indentation must be pixel-perfect
5. **SECTION HIERARCHY:** Headers, subheaders, and document flow must remain unchanged
6. **INTELLIGENT CONTENT REPLACEMENT:** Replace content thoughtfully while respecting structural boundaries

🎯 **STRUCTURE-FIRST REPLACEMENT PROTOCOL:**

**DOCUMENT STRUCTURE ANALYSIS:**
- Identify all content control anchors and field boundaries
- Map table structures, cell relationships, and formatting patterns
- Preserve document hierarchy (headers, sections, subsections)
- Maintain spacing, indentation, and visual layout exactly
- Respect content control markers and field labels

**INTELLIGENT CONTENT REPLACEMENT - AGGRESSIVE TEMPLATE CLEANUP:**
- "Azure Middleware Integration" → User's actual project name OR "[PROJECT NAME]"
- "Toll Brothers" → User's actual client company OR "[CLIENT COMPANY]"
- Template project descriptions → User's project description OR "[PROJECT DESCRIPTION TO BE PROVIDED]"
- Template deliverables → User's actual deliverables OR "[DELIVERABLES TO BE DEFINED]"
- Template timelines → User's actual timeline OR "[TIMELINE TO BE DETERMINED]"
- Template costs → User's actual budget OR "[BUDGET TO BE DETERMINED]"
- Template contact details → User's actual contact information OR "[CLIENT CONTACT INFORMATION]"

**🧹 TEMPLATE CONTENT CLEANUP PROTOCOL:**
- Remove ALL template-specific company names, project names, and example content
- Replace template examples with clean, professional placeholders
- Clean up any leftover template data that doesn't match the user's actual project
- Examples of content to ALWAYS replace:
  * Any specific company names from the template (e.g., "Toll Brothers", "Microsoft", "Google")
  * Any specific project names from the template (e.g., "Azure Middleware Integration", "Website Redesign")
  * Any template-specific dates, costs, or contact information
  * Any example deliverables or requirements that don't match user's project
- When replacing, use appropriate placeholders like "[CLIENT COMPANY]", "[PROJECT NAME]", "[DELIVERABLE 1]"

**🏗️ CONTENT CONTROL ANCHOR PRESERVATION:**
- Field labels like "Company:" are STRUCTURAL ELEMENTS - never modify them
- Content follows after the anchor: "Company: [CONTENT GOES HERE]"
- Table cell boundaries are SACRED: "| **Company:** | [CONTENT] |"
- Never break field labels or merge content across structural boundaries
- Respect the exact spacing and formatting around content control anchors

**🏛️ TABLE ARCHITECTURE PRESERVATION:**
- Table pipe separators (|) are STRUCTURAL PILLARS - removing them collapses the document
- Each table cell is a CONTENT CONTAINER - never merge or break containers
- Table row separators (dashes) define the TABLE FOUNDATION - preserve exactly
- Example of CORRECT preservation: "| **Company:** | QuantumRhino | **Date:** | January 20, 2025 |"
- Example of STRUCTURE DESTRUCTION: "**Company:**QuantumRhino**Date:**January 20, 2025"
- Maintain table alignment, spacing, and visual structure precisely

🎯 **INTELLIGENT CONTENT HANDLING:**
- When user data is missing: Use professional placeholders like "[PROJECT NAME]", "[CLIENT COMPANY]"
- Never keep template examples like "Azure Middleware Integration" or "Toll Brothers"
- Replace template-specific content with appropriate placeholders or user data
- Maintain professional tone and document integrity

🧹 **AGGRESSIVE TEMPLATE CLEANUP MANDATE:**
- SCAN EVERY LINE for template-specific content that doesn't belong to the user's project
- REPLACE any company names, project names, or examples from the original template
- CLEAN UP leftover template data like sample deliverables, example timelines, placeholder costs
- ENSURE the final document is ready for the user's actual project information
- NEVER leave template artifacts that would confuse or mislead the user
- PRIORITIZE clean, professional placeholders over outdated template examples

**SYSTEMATIC PLACEHOLDER REPLACEMENT:**
- [CLIENT_NAME] → Real client name OR "[CLIENT NAME]"
- [CLIENT_COMPANY] → Real client company OR "[CLIENT COMPANY]"
- [PROJECT_NAME] → Real project name OR "[PROJECT NAME]"
- [CLIENT_EMAIL] → Real client email OR "[CLIENT EMAIL]"
- [START_DATE] → Real start date OR "[START DATE]"
- [END_DATE] → Real end date OR "[END DATE]"
- [TOTAL_COST] → Real budget amount OR "[TOTAL COST]"
- [Date] → Today's date (${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })})
- [TODAY] → Today's date (${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })})
- [CURRENT_DATE] → Today's date (${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })})
- [SIGNATURE_DATE] → Today's date (${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })})
- Vendor/Provider fields → Company information provided

**CONTENT TRANSFORMATION METHODOLOGY:**
- STRUCTURE FIRST: Preserve all formatting, tables, headers, spacing
- CONTENT SECOND: Replace template content with user data or professional placeholders
- BOUNDARY RESPECT: Never break content control anchors or field boundaries
- VISUAL FIDELITY: Maintain exact visual appearance and document flow

**🏗️ ABSOLUTE STRUCTURE PRESERVATION PROTOCOL:**

1. **DOCUMENT ARCHITECTURE INTEGRITY:** Every structural element must remain identical
2. **CONTENT CONTROL SANCTITY:** Field labels, anchors, and boundaries are untouchable
3. **TABLE INFRASTRUCTURE:** All pipes (|), dashes (-), spacing, and alignment preserved exactly
4. **FORMATTING FIDELITY:** Bold (**text**), italic (*text*), spacing copied character-by-character
5. **VISUAL LAYOUT PRESERVATION:** Headers, indentation, line breaks, and document flow unchanged
6. **SIGNATURE STRUCTURE:** Signature sections maintain exact formatting with only content updates

**STRUCTURAL PRESERVATION RULES:**
✅ **FIELD LABEL PROTECTION:** "Company:" labels are structural - never modify
✅ **CONTENT CONTROL BOUNDARIES:** Replace content AFTER the anchor, never break the anchor
✅ **TABLE CELL INTEGRITY:** Each cell remains separate with proper | separators
✅ **SPACING PRECISION:** Maintain exact spacing, tabs, and indentation
✅ **HEADER HIERARCHY:** All # headers and section structure preserved
✅ **LIST FORMATTING:** Bullet points, numbering, and list structure unchanged

**ABSOLUTELY FORBIDDEN STRUCTURAL VIOLATIONS:**
❌ Breaking field labels or content control anchors
❌ Removing table pipe separators (|) - destroys document structure
❌ Merging table cells or changing table architecture
❌ Adding explanatory notes or comments
❌ Breaking words across lines or adding random formatting
❌ Changing document hierarchy or section structure
❌ Modifying spacing, indentation, or visual layout
❌ Converting structured tables to unstructured text
❌ Disrupting content control boundaries or field relationships

🏢 **COMPANY INFORMATION** (Use for ALL vendor/provider/contractor fields):
- Company Name: ${companyInfo.companyName}
- Contact Name: ${companyInfo.contactName}
- Email: ${companyInfo.email}
- Phone: ${companyInfo.phone}
- Address: ${companyInfo.address}
- Website: ${companyInfo.website}

📋 **USER'S PROJECT INFORMATION** (Use this data to replace ALL placeholder content):
${processedPrompt}

🚨 **HANDLING MISSING PROJECT INFORMATION:**
If the user has not provided specific project details:
- STILL CLEAR all template content like "Azure Middleware Integration", "Toll Brothers"
- REPLACE with professional placeholders: "[PROJECT NAME]", "[CLIENT COMPANY]", "[PROJECT DESCRIPTION TO BE PROVIDED]"
- NEVER leave template examples in the document
- Use generic professional language instead of template-specific content
- Example: "Azure Middleware Integration" → "[PROJECT NAME]" (NOT kept as template example)

📝 **TEMPLATE TO UPDATE** (Replace ALL content with real data above):
${markdown}

🏗️ **STRUCTURE-PRESERVING TRANSFORMATION METHODOLOGY:**

1. **DOCUMENT STRUCTURE ANALYSIS:**
   - Map all content control anchors and field boundaries
   - Identify table structures, cell relationships, and formatting patterns
   - Catalog headers, sections, and document hierarchy
   - Note spacing, indentation, and visual layout elements

2. **CONTENT IDENTIFICATION AND MAPPING:**
   - Locate ALL template-specific content: "Azure Middleware Integration", "Toll Brothers", etc.
   - Identify placeholder patterns: [CLIENT_NAME], [PROJECT_NAME], [Date], etc.
   - Map content to structural containers (table cells, sections, fields)
   - Preserve the relationship between content and its structural container
   - **AGGRESSIVE TEMPLATE CLEANUP:** Scan for any leftover template examples, sample data, or irrelevant content
   - Look for template artifacts like example dates, sample costs, placeholder descriptions
   - Identify content that clearly belongs to a different project/client and mark for replacement

3. **INTELLIGENT CONTENT REPLACEMENT:**
   - Template project names → User's project name OR "[PROJECT NAME]"
   - Template company names → User's client company OR "[CLIENT COMPANY]"
   - Template descriptions → User's project details OR "[PROJECT DESCRIPTION TO BE PROVIDED]"
   - Template deliverables → User's deliverables OR "[DELIVERABLES TO BE DEFINED]"
   - Template timelines → User's timeline OR "[TIMELINE TO BE DETERMINED]"
   - Template costs → User's budget OR "[BUDGET TO BE DETERMINED]"

   **🎯 SPECIFIC CLEANUP TARGETS:**
   - Replace any specific company names with "[CLIENT COMPANY]" if user hasn't provided client info
   - Replace any specific project titles with "[PROJECT NAME]" if user hasn't provided project name
   - Replace template-specific deliverables with generic placeholders like "[DELIVERABLE 1]", "[DELIVERABLE 2]"
   - Replace template dates with "[START DATE]", "[END DATE]", "[COMPLETION DATE]"
   - Replace template costs/budgets with "[PROJECT COST]", "[HOURLY RATE]", "[TOTAL BUDGET]"
   - Replace template contact info with "[CLIENT CONTACT]", "[CLIENT EMAIL]", "[CLIENT PHONE]"
   - Clean up any leftover template examples that don't match the user's actual project scope

4. **SYSTEMATIC PLACEHOLDER PROCESSING:**
   - [Date] → ${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
   - [TODAY] → ${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
   - [CURRENT_DATE] → ${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
   - [SIGNATURE_DATE] → ${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
   - All other placeholders → User data OR professional placeholder

5. **STRUCTURE INTEGRITY VALIDATION:**
   - Verify all table pipes (|) and cell boundaries remain intact
   - Confirm field labels and content control anchors are preserved
   - Ensure spacing, indentation, and formatting remain identical
   - Validate that content fits properly within structural containers
   - Check that document hierarchy and section flow are unchanged

6. **SIGNATURE AND DATE FIELD PROCESSING:**
   - Signature tables maintain exact structure with content updates only
   - Date fields get current date: ${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
   - Common date patterns: "Date: ___", "Date: [DATE]", "Signed: ___" → Current date
   - Preserve signature table formatting while updating content appropriately

7. **FINAL STRUCTURE AND CONTENT VALIDATION:**
   - Document structure remains architecturally identical to original
   - All content control anchors and field boundaries intact
   - Template content replaced with user data or professional placeholders
   - Table structures, spacing, and formatting preserved exactly
   - No broken words, added notes, or structural modifications
   - Date fields properly filled with current date
   - Document maintains professional appearance and structural integrity

**🏗️ STRUCTURE PRESERVATION SUCCESS CRITERIA - ALL MUST PASS:**

✅ **DOCUMENT ARCHITECTURE INTACT:** All structural elements preserved exactly as original
✅ **CONTENT CONTROL ANCHORS PRESERVED:** Field labels and boundaries remain untouched
✅ **TABLE INFRASTRUCTURE MAINTAINED:** All pipes (|), dashes (-), and cell boundaries intact
✅ **FORMATTING FIDELITY ACHIEVED:** Bold, italic, spacing, indentation identical to original
✅ **CONTENT INTELLIGENTLY REPLACED:** Template content replaced with user data or professional placeholders
✅ **PLACEHOLDER PROCESSING COMPLETE:** All [PLACEHOLDER] patterns properly handled
✅ **DATE FIELDS POPULATED:** Current date filled in all signature and date fields
✅ **VISUAL LAYOUT PRESERVED:** Headers, sections, spacing, and document flow unchanged
✅ **NO STRUCTURAL VIOLATIONS:** Zero additions, deletions, or modifications to document structure
✅ **PROFESSIONAL INTEGRITY MAINTAINED:** Document remains polished and properly formatted

**CRITICAL ERROR PREVENTION CHECKLIST:**
❌ **STRUCTURAL DAMAGE:** Table pipes (|) removed or cell boundaries broken
❌ **CONTENT CONTROL VIOLATIONS:** Field labels modified or anchors disrupted
❌ **FORMATTING CORRUPTION:** Spacing, indentation, or visual layout changed
❌ **TEMPLATE REMNANTS:** "Azure Middleware", "Toll Brothers", or ANY template-specific content remaining
❌ **LEFTOVER TEMPLATE DATA:** Sample deliverables, example timelines, placeholder costs, or template contact info
❌ **IRRELEVANT CONTENT:** Any content from the original template that doesn't apply to user's project
❌ **BROKEN ELEMENTS:** Words split across lines or incomplete formatting
❌ **UNAUTHORIZED ADDITIONS:** Notes, comments, or explanatory text added
❌ **PLACEHOLDER FAILURES:** [Date], [TODAY], or other placeholders left unfilled
❌ **HIERARCHY DISRUPTION:** Headers, sections, or document structure modified
❌ **PROFESSIONAL DEGRADATION:** Document quality or appearance compromised

**🎯 FINAL PROCESSING INSTRUCTIONS:**
- Structure preservation is the PRIMARY objective
- Content replacement is SECONDARY to maintaining document integrity
- If user provides project info → Use their actual project information within existing structure
- **MANDATORY TEMPLATE CLEANUP:** Remove ALL template-specific content and replace with clean placeholders
- **AGGRESSIVE CONTENT SCANNING:** Look for any leftover template examples, sample data, or irrelevant information
- **PROFESSIONAL PLACEHOLDER USAGE:** Use clean, professional placeholders like "[PROJECT NAME]", "[CLIENT COMPANY]" instead of template examples
- If user provides NO project info → Use professional placeholders within existing structure
- Template content must be cleared but NEVER at the expense of document structure
- Return ONLY the updated document with structure preserved and content appropriately replaced

**CRITICAL REMINDER:** You are a STRUCTURE PRESERVATIONIST first, content replacer second. The document's architectural integrity is paramount.

Return ONLY the updated document with structure preserved and content intelligently replaced:
`;

    // Generate content
    console.log('Calling Gemini API with prompt length:', geminiPrompt.length);

    try {
      // Step 1: Generate initial content
      console.log('🤖 GEMINI: Calling API with enhanced formatting validation...');
      const result = await model.generateContent(geminiPrompt);
      console.log('✅ GEMINI: API call successful');

      const response = await result.response;
      let initialContent = response.text().trim();

      console.log('📊 GEMINI: Initial content generated, length:', initialContent.length);

      // Step 2: Self-validation comparison
      console.log('🔍 GEMINI: Starting self-validation comparison...');

      const validationPrompt = `
You are a SILENT STRUCTURE VALIDATOR. Your job is to ensure perfect document structure integrity.

DOCUMENT TO VALIDATE:
${initialContent}

STRUCTURE VALIDATION CHECKLIST:
- ✅ All table pipe separators (|) present and properly positioned
- ✅ Table cell boundaries intact with proper spacing
- ✅ Content control anchors and field labels preserved
- ✅ No broken words or formatting corruption
- ✅ Headers, spacing, and indentation exactly maintained
- ✅ No explanatory notes or comments added

CRITICAL FIXES NEEDED:
- If table cells are merged: "**Company:**Value**Date:**Value" → "| **Company:** | Value | **Date:** | Value |"
- If pipes are missing: Restore proper table structure with | separators
- If words are broken: Fix to complete words
- If spacing is wrong: Restore exact spacing and indentation
- Remove any comparison notes or explanations

Return ONLY the structurally perfect document with no additional text:

VALIDATED DOCUMENT:`;

      // Step 3: Get validated/corrected content
      const validationResult = await model.generateContent(validationPrompt);
      const validationResponse = await validationResult.response;
      let finalContent = validationResponse.text().trim();

      // Additional cleanup: Remove any comparison notes that might have slipped through
      finalContent = finalContent
        .replace(/^The generated document has.*$/gm, '')
        .replace(/^Otherwise, the formatting is.*$/gm, '')
        .replace(/^The generated document is returned unchanged\.$/gm, '')
        .replace(/^In the .* section, the original template.*$/gm, '')
        .replace(/^This is a content change, not a formatting issue.*$/gm, '')
        .replace(/^\s*$/gm, '') // Remove empty lines created by removals
        .trim();

      console.log('📊 GEMINI: Final validated content length:', finalContent.length);
      console.log('✅ GEMINI: Self-validation and cleanup complete');

      // Structure comparison for logging
      console.log('🔍 VALIDATION: Performing structure comparison...');
      const structureValidation = validateStructureMatch(originalStructure, finalContent);

      if (!structureValidation.isMatch) {
        console.warn('⚠️ VALIDATION: Minor structure differences detected:', structureValidation.issues);
        console.log('✅ VALIDATION: Proceeding with validated content');
      } else {
        console.log('✅ VALIDATION: Perfect structure match achieved');
      }

      return NextResponse.json({
        updatedMarkdown: finalContent,
        message: 'SOW updated with self-validated formatting preservation',
        validation: {
          ...structureValidation,
          selfValidated: true
        }
      });
    } catch (geminiError) {
      console.error('Gemini API specific error:', geminiError);

      // Fallback to mock response if Gemini fails
      console.log('Falling back to mock response due to Gemini error');
      return NextResponse.json({
        updatedMarkdown: `${markdown}\n\n<!-- Fallback Mode: Gemini API failed, prompt received: ${prompt.substring(0, 100)}... -->`,
        message: 'SOW updated with fallback processing (Gemini API unavailable)'
      });
    }

  } catch (error) {
    console.error('General API error:', error);
    return NextResponse.json(
      { error: 'Failed to process request: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}