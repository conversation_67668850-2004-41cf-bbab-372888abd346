import { NextRequest, NextResponse } from 'next/server';
import { writeFile, readFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';

interface SavedTemplate {
  id: string;
  name: string;
  originalTemplateId: string; // Reference to the original uploaded template
  sections: any[];
  createdAt: string;
  lastUsed?: string;
  usageCount: number;
  savedFormData?: any; // Store form data from SOW generator
}

// Get templates file path - use Railway storage path for consistency
function getTemplatesFilePath(): string {
  const storageRoot = process.env.STORAGE_PATH || join(process.cwd(), 'storage');
  return join(storageRoot, 'saved-templates.json');
}

// Load existing templates
async function loadTemplates(): Promise<SavedTemplate[]> {
  try {
    const templatesPath = getTemplatesFilePath();
    const data = await readFile(templatesPath, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    // File doesn't exist or is empty, return empty array
    return [];
  }
}

// Save templates to file
async function saveTemplates(templates: SavedTemplate[]): Promise<void> {
  const templatesPath = getTemplatesFilePath();
  const storageRoot = process.env.STORAGE_PATH || join(process.cwd(), 'storage');

  // Ensure storage directory exists
  try {
    await mkdir(storageRoot, { recursive: true });
  } catch (error) {
    // Directory might already exist
  }

  await writeFile(templatesPath, JSON.stringify(templates, null, 2));
  console.log('✅ Template saved to:', templatesPath);
}

export async function POST(request: NextRequest) {
  try {
    const { name, originalTemplateId, sections, formData } = await request.json();

    if (!name || !originalTemplateId) {
      return NextResponse.json({ error: 'Name and original template ID are required' }, { status: 400 });
    }

    console.log('💾 Saving template reference:', { name, originalTemplateId, hasFormData: !!formData });

    // Load existing templates
    const templates = await loadTemplates();
    console.log('📂 Loaded existing templates:', templates.length);

    // Create new template reference
    const newTemplate: SavedTemplate = {
      id: uuidv4(),
      name: name,
      originalTemplateId: originalTemplateId, // Reference to original uploaded template
      sections: sections || [],
      createdAt: new Date().toISOString(),
      usageCount: 0,
      savedFormData: formData || null // Store the form data from SOW generator
    };

    // Add to templates array
    templates.push(newTemplate);

    // Save back to file
    await saveTemplates(templates);
    console.log('✅ Template saved successfully:', newTemplate.id);

    return NextResponse.json({
      id: newTemplate.id,
      message: 'Template saved successfully'
    });

  } catch (error) {
    console.error('❌ Template save error:', error);
    return NextResponse.json(
      { error: 'Failed to save template' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    console.log('📂 SAVED TEMPLATES: Loading saved templates...');

    const storageRoot = process.env.STORAGE_PATH || join(process.cwd(), 'storage');
    const templatesPath = getTemplatesFilePath();
    console.log('📂 SAVED TEMPLATES: Using storage directory:', storageRoot);
    console.log('🌍 SAVED TEMPLATES: STORAGE_PATH env var:', process.env.STORAGE_PATH || 'not set');
    console.log('🔍 SAVED TEMPLATES: Looking for templates at:', templatesPath);

    const templates = await loadTemplates();
    console.log('✅ SAVED TEMPLATES: Loaded templates:', templates.length);
    console.log('📋 SAVED TEMPLATES: Template IDs:', templates.map(t => t.id));

    // Sort by most recently used, then by creation date
    templates.sort((a, b) => {
      if (a.lastUsed && b.lastUsed) {
        return new Date(b.lastUsed).getTime() - new Date(a.lastUsed).getTime();
      }
      if (a.lastUsed && !b.lastUsed) return -1;
      if (!a.lastUsed && b.lastUsed) return 1;
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });

    return NextResponse.json(templates);

  } catch (error) {
    console.error('❌ SAVED TEMPLATES: Template load error:', error);
    return NextResponse.json(
      { error: 'Failed to load templates' },
      { status: 500 }
    );
  }
}

// DELETE - Remove a saved template
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const templateId = searchParams.get('id');

    if (!templateId) {
      console.log('❌ SAVED TEMPLATE DELETE: No template ID provided');
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    console.log('🗑️ SAVED TEMPLATE DELETE: Starting deletion of saved template:', templateId);

    // Load existing templates
    const templates = await loadTemplates();
    console.log('📂 SAVED TEMPLATE DELETE: Found templates before deletion:', templates.length);
    console.log('📋 SAVED TEMPLATE DELETE: Available template IDs:', templates.map(t => t.id));

    // Find the template to delete
    const templateToDelete = templates.find(t => t.id === templateId);

    // Filter out the template to delete
    const updatedTemplates = templates.filter(t => t.id !== templateId);

    if (updatedTemplates.length === templates.length) {
      console.log('❌ SAVED TEMPLATE DELETE: Template not found:', templateId);
      return NextResponse.json(
        { error: 'Saved template not found' },
        { status: 404 }
      );
    }

    console.log('🎯 SAVED TEMPLATE DELETE: Found template to delete:', templateToDelete?.name);

    // Save updated templates list
    try {
      await saveTemplates(updatedTemplates);
      console.log('✅ SAVED TEMPLATE DELETE: Template deleted successfully. Remaining templates:', updatedTemplates.length);
    } catch (error) {
      console.error('❌ SAVED TEMPLATE DELETE: Failed to save updated templates list:', error);
      return NextResponse.json(
        { error: 'Failed to update saved templates list after deletion' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Saved template deleted successfully',
      remainingCount: updatedTemplates.length,
      deletedTemplate: templateToDelete?.name
    });

  } catch (error) {
    console.error('❌ SAVED TEMPLATE DELETE: Failed to delete template:', error);
    return NextResponse.json(
      { error: 'Failed to delete saved template', details: String(error) },
      { status: 500 }
    );
  }
}
