import { NextRequest, NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join } from 'path';

interface SavedTemplate {
  id: string;
  name: string;
  originalTemplateId: string; // Reference to the original uploaded template
  sections: any[];
  createdAt: string;
  lastUsed?: string;
  usageCount: number;
  savedFormData?: any;
}

async function loadTemplates(): Promise<SavedTemplate[]> {
  try {
    const storageRoot = process.env.STORAGE_PATH || join(process.cwd(), 'storage');
    const templatesPath = join(storageRoot, 'saved-templates.json');
    const data = await readFile(templatesPath, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    return [];
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    if (!id) {
      console.error('❌ SAVED TEMPLATE: No template ID provided');
      return NextResponse.json({ error: 'Template ID is required' }, { status: 400 });
    }

    console.log('🔍 SAVED TEMPLATE: Loading template:', id);

    const storageRoot = process.env.STORAGE_PATH || join(process.cwd(), 'storage');
    console.log('📂 SAVED TEMPLATE: Using storage directory:', storageRoot);
    console.log('🌍 SAVED TEMPLATE: STORAGE_PATH env var:', process.env.STORAGE_PATH || 'not set');

    // Load all templates
    const templates = await loadTemplates();
    console.log('📂 SAVED TEMPLATE: Found templates:', templates.length);

    // Find the specific template
    const template = templates.find(t => t.id === id);

    if (!template) {
      console.error('❌ SAVED TEMPLATE: Template not found:', id);
      console.error('❌ SAVED TEMPLATE: Available template IDs:', templates.map(t => t.id));
      return NextResponse.json({
        error: 'Template not found',
        templateId: id,
        availableTemplates: templates.map(t => ({ id: t.id, name: t.name }))
      }, { status: 404 });
    }

    console.log('✅ SAVED TEMPLATE: Template found:', template.name);
    console.log('🔗 SAVED TEMPLATE: Original template ID:', template.originalTemplateId);

    // Update last used timestamp and usage count
    template.lastUsed = new Date().toISOString();
    template.usageCount = (template.usageCount || 0) + 1;

    // Save updated templates back to file (FIXED: Use correct storage path for Railway)
    try {
      const { writeFile } = await import('fs/promises');
      const storageRoot = process.env.STORAGE_PATH || join(process.cwd(), 'storage');
      const templatesPath = join(storageRoot, 'saved-templates.json');
      console.log('💾 SAVED TEMPLATE: Updating usage stats at:', templatesPath);
      await writeFile(templatesPath, JSON.stringify(templates, null, 2));
      console.log('✅ SAVED TEMPLATE: Usage stats updated successfully');
    } catch (error) {
      console.warn('⚠️ SAVED TEMPLATE: Failed to update template usage stats:', error);
    }

    // Load the original template data
    console.log('📂 SAVED TEMPLATE: Loading original template data...');
    try {
      const uploadsDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'uploads') : join(process.cwd(), 'uploads');
      const originalMetadataPath = join(uploadsDir, `${template.originalTemplateId}.json`);

      const originalMetadata = JSON.parse(await readFile(originalMetadataPath, 'utf-8'));
      console.log('✅ SAVED TEMPLATE: Original template metadata loaded:', originalMetadata.name);

      // Return the original template data with saved form data attached
      return NextResponse.json({
        id: template.id, // Keep the saved template ID for tracking
        name: template.name, // Use the saved template name
        markdown: originalMetadata.originalMarkdown, // Use original template markdown
        fields: originalMetadata.placeholders || [],
        originalTemplateId: template.originalTemplateId,
        savedFormData: template.savedFormData, // Include saved form data
        createdAt: template.createdAt,
        lastUsed: template.lastUsed,
        usageCount: template.usageCount
      });

    } catch (originalError) {
      console.error('❌ SAVED TEMPLATE: Failed to load original template:', originalError);
      return NextResponse.json({
        error: 'Original template not found',
        templateId: id,
        originalTemplateId: template.originalTemplateId,
        details: 'The original template file may have been deleted'
      }, { status: 404 });
    }

  } catch (error) {
    console.error('Template retrieval error:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve template' },
      { status: 500 }
    );
  }
}
