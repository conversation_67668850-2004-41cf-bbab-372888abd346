import { NextRequest, NextResponse } from 'next/server';
import { readFile, writeFile, unlink, mkdir } from 'fs/promises';
import { join } from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// GET - Serve template preview (original DOCX or generated from saved template)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const resolvedParams = await params;
  try {
    const templateId = resolvedParams.id;

    if (!templateId) {
      return NextResponse.json({ error: 'Template ID is required' }, { status: 400 });
    }

    console.log('🔍 TEMPLATE PREVIEW: Looking up template:', templateId);

    const storageRoot = process.env.STORAGE_PATH || process.cwd();
    const uploadsDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'uploads') : join(process.cwd(), 'uploads');

    // STEP 1: Try to find original uploaded DOCX file
    const docxPath = join(uploadsDir, `${templateId}.docx`);
    
    try {
      const docxBuffer = await readFile(docxPath);
      console.log('✅ TEMPLATE PREVIEW: Found original DOCX file, size:', docxBuffer.length, 'bytes');
      
      // Get filename from metadata
      let filename = `template-${templateId}.docx`;
      try {
        const metadataPath = join(uploadsDir, `${templateId}.json`);
        const metadata = JSON.parse(await readFile(metadataPath, 'utf-8'));
        filename = metadata.name || filename;
      } catch (e) {
        // Use default filename
      }

      return new NextResponse(docxBuffer, {
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'Content-Disposition': `inline; filename="${filename}"`,
          'Content-Length': docxBuffer.length.toString(),
        },
      });

    } catch (docxError) {
      console.log('📄 TEMPLATE PREVIEW: No original DOCX found, checking saved templates...');
      
      // STEP 2: Check if this is a saved template (JSON-based)
      try {
        const savedTemplatesPath = join(storageRoot, 'saved-templates.json');
        const savedTemplatesData = await readFile(savedTemplatesPath, 'utf-8');
        const savedTemplates = JSON.parse(savedTemplatesData);

        const savedTemplate = savedTemplates.find((t: any) => t.id === templateId);

        if (!savedTemplate) {
          console.log('❌ TEMPLATE PREVIEW: Template not found in any storage system');
          return NextResponse.json({ error: 'Template not found' }, { status: 404 });
        }

        console.log('✅ TEMPLATE PREVIEW: Found saved template:', savedTemplate.name);
        console.log('🔗 TEMPLATE PREVIEW: Original template ID:', savedTemplate.originalTemplateId);

        // Load the original DOCX file that this saved template references
        const originalDocxPath = join(uploadsDir, `${savedTemplate.originalTemplateId}.docx`);

        try {
          const originalDocxBuffer = await readFile(originalDocxPath);
          console.log('✅ TEMPLATE PREVIEW: Found original DOCX file, size:', originalDocxBuffer.length, 'bytes');

          return new NextResponse(originalDocxBuffer, {
            headers: {
              'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
              'Content-Disposition': `inline; filename="${savedTemplate.name || templateId}.docx"`,
              'Content-Length': originalDocxBuffer.length.toString(),
            },
          });

        } catch (originalDocxError) {
          console.error('❌ TEMPLATE PREVIEW: Original DOCX file not found:', originalDocxError);
          return NextResponse.json({
            error: 'Original template file not found',
            templateId: templateId,
            originalTemplateId: savedTemplate.originalTemplateId,
            details: 'The original template file may have been deleted'
          }, { status: 404 });
        }

      } catch (savedTemplateError) {
        console.error('❌ TEMPLATE PREVIEW: Saved template processing failed:', savedTemplateError);
        return NextResponse.json(
          { error: 'Failed to process template' },
          { status: 500 }
        );
      }
    }

  } catch (error) {
    console.error('❌ TEMPLATE PREVIEW: Unexpected error:', error);
    return NextResponse.json(
      { error: 'Failed to load template preview: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}
