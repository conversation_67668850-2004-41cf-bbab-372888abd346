import { NextRequest, NextResponse } from 'next/server';
import { readFile, writeFile, unlink } from 'fs/promises';
import { join } from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// GET - Serve the original DOCX file for preview
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const resolvedParams = await params;
  try {
    const templateId = resolvedParams.id;

    console.log('🔍 DOCX PREVIEW: === STARTING TEMPLATE LOOKUP ===');
    console.log('🔍 DOCX PREVIEW: Template ID:', templateId);
    console.log('🔍 DOCX PREVIEW: Request URL:', request.url);
    console.log('🔍 DOCX PREVIEW: STORAGE_PATH env:', process.env.STORAGE_PATH || 'NOT SET');
    console.log('🔍 DOCX PREVIEW: Current working directory:', process.cwd());

    if (!templateId) {
      console.error('❌ DOCX PREVIEW: No template ID provided');
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    // FIRST: Check saved templates system (Railway storage)
    try {
      const storageRoot = process.env.STORAGE_PATH || join(process.cwd(), 'storage');
      const savedTemplatesPath = join(storageRoot, 'saved-templates.json');
      console.log('🔍 DOCX PREVIEW: Checking saved templates at:', savedTemplatesPath);

      const savedTemplatesData = await readFile(savedTemplatesPath, 'utf-8');
      const savedTemplates = JSON.parse(savedTemplatesData);
      console.log('🔍 DOCX PREVIEW: Found', savedTemplates.length, 'saved templates');

      const savedTemplate = savedTemplates.find((t: any) => t.id === templateId);

      if (savedTemplate) {
        console.log('✅ DOCX PREVIEW: Found saved template:', savedTemplate.name);
        console.log('🔍 DOCX PREVIEW: Markdown length:', savedTemplate.markdown?.length || 0);

        if (!savedTemplate.markdown) {
          throw new Error('Saved template has no markdown content');
        }

        // Generate DOCX from markdown using pandoc
        const tempMarkdownPath = join(storageRoot, `temp-${templateId}-${Date.now()}.md`);
        const tempDocxPath = join(storageRoot, `temp-${templateId}-${Date.now()}.docx`);

        console.log('🔄 DOCX PREVIEW: Generating DOCX from saved template markdown...');

        // Write markdown to temp file
        await writeFile(tempMarkdownPath, savedTemplate.markdown, 'utf-8');

        // Convert to DOCX using pandoc
        const pandocCommand = `pandoc "${tempMarkdownPath}" -f markdown -t docx -o "${tempDocxPath}"`;
        console.log('🔄 DOCX PREVIEW: Running pandoc:', pandocCommand);
        await execAsync(pandocCommand);

        // Read the generated DOCX
        const docxBuffer = await readFile(tempDocxPath);

        // Clean up temp files
        try {
          await unlink(tempMarkdownPath);
          await unlink(tempDocxPath);
        } catch (cleanupError) {
          console.warn('Failed to clean up temp files:', cleanupError);
        }

        console.log('✅ DOCX PREVIEW: Generated DOCX from saved template, size:', docxBuffer.length, 'bytes');

        return new NextResponse(docxBuffer, {
          headers: {
            'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'Content-Disposition': `inline; filename="${savedTemplate.name || templateId}.docx"`,
            'Content-Length': docxBuffer.length.toString(),
          },
        });
      } else {
        console.log('⚠️ DOCX PREVIEW: Template not found in saved templates');
      }
    } catch (savedTemplateError) {
      console.log('⚠️ DOCX PREVIEW: Error checking saved templates:', savedTemplateError.message);
    }

    // Try multiple storage locations for uploaded templates
    const storageRoot = process.env.STORAGE_PATH || process.cwd();

    // Location 1: uploads directory (old system)
    const uploadsDir = join(storageRoot, 'uploads');
    const docxPath = join(uploadsDir, `${templateId}.docx`);

    // Location 2: templates directory (new system - but we need to check all user subdirs)
    const templatesDir = join(storageRoot, 'templates');

    // Location 3: storage/templates directory (alternative path)
    const storageTemplatesDir = join(storageRoot, 'storage', 'templates');

    console.log('🔍 DOCX PREVIEW: Will check these locations:');
    console.log('  1. Uploads:', docxPath);
    console.log('  2. Templates root:', join(templatesDir, `${templateId}.docx`));
    console.log('  3. Storage/templates:', join(storageTemplatesDir, `${templateId}.docx`));

    console.log('🔍 DOCX PREVIEW: Trying uploaded templates system');
    console.log('🔍 DOCX PREVIEW: Uploads directory:', uploadsDir);
    console.log('🔍 DOCX PREVIEW: Looking for DOCX file at:', docxPath);

    // Check if uploads directory exists and list contents
    try {
      const { access, constants, readdir } = await import('fs/promises');
      await access(uploadsDir, constants.F_OK);
      console.log('✅ DOCX PREVIEW: Uploads directory exists');

      const files = await readdir(uploadsDir);
      console.log('🔍 DOCX PREVIEW: Files in uploads directory:', files);
      console.log('🔍 DOCX PREVIEW: Looking for file:', `${templateId}.docx`);
    } catch (dirError) {
      console.log('❌ DOCX PREVIEW: Uploads directory error:', dirError);
    }

    // Also check storage/templates directory (FIXED: Don't double up storage path)
    const altStorageTemplatesDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'templates') : join(process.cwd(), 'storage', 'templates');
    console.log('🔍 DOCX PREVIEW: Also checking storage/templates directory:', altStorageTemplatesDir);
    try {
      const { access, constants, readdir } = await import('fs/promises');
      await access(altStorageTemplatesDir, constants.F_OK);
      const storageFiles = await readdir(altStorageTemplatesDir);
      console.log('🔍 DOCX PREVIEW: Files in storage/templates:', storageFiles);
    } catch (storageError) {
      console.log('❌ DOCX PREVIEW: Storage/templates directory error:', storageError);
    }

    try {
      let docxBuffer: Buffer;
      let foundLocation = '';

      // Try Location 1: uploads directory (old system)
      try {
        console.log('🔍 DOCX PREVIEW: Trying uploads directory...');
        docxBuffer = await readFile(docxPath);
        foundLocation = 'uploads';
        console.log('✅ DOCX PREVIEW: Found in uploads directory, size:', docxBuffer.length, 'bytes');
      } catch (uploadsError) {
        console.log('⚠️ DOCX PREVIEW: Not found in uploads:', uploadsError.message);

        // Try Location 2: templates root directory
        try {
          const templatesRootPath = join(templatesDir, `${templateId}.docx`);
          console.log('🔍 DOCX PREVIEW: Trying templates root:', templatesRootPath);
          docxBuffer = await readFile(templatesRootPath);
          foundLocation = 'templates-root';
          console.log('✅ DOCX PREVIEW: Found in templates root, size:', docxBuffer.length, 'bytes');
        } catch (templatesRootError) {
          console.log('⚠️ DOCX PREVIEW: Not found in templates root:', templatesRootError.message);

          // Try Location 3: storage/templates directory
          try {
            const storageTemplatesPath = join(storageTemplatesDir, `${templateId}.docx`);
            console.log('🔍 DOCX PREVIEW: Trying storage/templates:', storageTemplatesPath);
            docxBuffer = await readFile(storageTemplatesPath);
            foundLocation = 'storage-templates';
            console.log('✅ DOCX PREVIEW: Found in storage/templates, size:', docxBuffer.length, 'bytes');
          } catch (storageTemplatesError) {
            console.log('⚠️ DOCX PREVIEW: Not found in storage/templates:', storageTemplatesError.message);

            // Try Location 4: Check all user subdirectories in templates
            try {
              console.log('🔍 DOCX PREVIEW: Searching all user subdirectories in templates...');
              const { readdir } = await import('fs/promises');
              const userDirs = await readdir(templatesDir);
              console.log('🔍 DOCX PREVIEW: Found user directories:', userDirs);

              for (const userDir of userDirs) {
                const userTemplatePath = join(templatesDir, userDir, `${templateId}.docx`);
                try {
                  console.log('🔍 DOCX PREVIEW: Trying user directory:', userTemplatePath);
                  docxBuffer = await readFile(userTemplatePath);
                  foundLocation = `templates/${userDir}`;
                  console.log('✅ DOCX PREVIEW: Found in user directory', userDir, ', size:', docxBuffer.length, 'bytes');
                  break;
                } catch (userDirError) {
                  // Continue to next user directory
                }
              }

              if (!docxBuffer) {
                throw new Error('Template not found in any location');
              }
            } catch (searchError) {
              throw new Error(`Template not found in any location: ${searchError.message}`);
            }
          }
        }
      }

      // Load template metadata for filename
      let filename = `template-${templateId}.docx`;
      try {
        const metadataPath = join(uploadsDir, `${templateId}.json`);
        const metadataContent = await readFile(metadataPath, 'utf-8');
        const metadata = JSON.parse(metadataContent);
        filename = metadata.name || filename;
      } catch (error) {
        console.warn('Could not load template metadata:', error);
      }

      // Return the DOCX file for preview/download
      return new NextResponse(docxBuffer, {
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'Content-Disposition': `inline; filename="${filename}"`,
          'Content-Length': docxBuffer.length.toString(),
        },
      });

    } catch (error) {
      console.error('❌ DOCX PREVIEW: Template file not found at:', docxPath);
      return NextResponse.json(
        { error: 'Template file not found' },
        { status: 404 }
      );
    }

  } catch (error) {
    console.error('Template preview error:', error);
    return NextResponse.json(
      { error: 'Failed to load template preview: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}
