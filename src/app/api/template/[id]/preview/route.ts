import { NextRequest, NextResponse } from 'next/server';
import { readFile, writeFile, unlink, mkdir } from 'fs/promises';
import { join } from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// GET - Serve template preview (original DOCX or generated from saved template)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const resolvedParams = await params;
  try {
    const templateId = resolvedParams.id;

    if (!templateId) {
      return NextResponse.json({ error: 'Template ID is required' }, { status: 400 });
    }

    console.log('🔍 TEMPLATE PREVIEW: Looking up template:', templateId);

    const storageRoot = process.env.STORAGE_PATH || process.cwd();
    const uploadsDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'uploads') : join(process.cwd(), 'uploads');

    // STEP 1: Try to find original uploaded DOCX file
    const docxPath = join(uploadsDir, `${templateId}.docx`);
    
    try {
      const docxBuffer = await readFile(docxPath);
      console.log('✅ TEMPLATE PREVIEW: Found original DOCX file, size:', docxBuffer.length, 'bytes');
      
      // Get filename from metadata
      let filename = `template-${templateId}.docx`;
      try {
        const metadataPath = join(uploadsDir, `${templateId}.json`);
        const metadata = JSON.parse(await readFile(metadataPath, 'utf-8'));
        filename = metadata.name || filename;
      } catch (e) {
        // Use default filename
      }

      return new NextResponse(docxBuffer, {
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'Content-Disposition': `inline; filename="${filename}"`,
          'Content-Length': docxBuffer.length.toString(),
        },
      });

    } catch (docxError) {
      console.log('📄 TEMPLATE PREVIEW: No original DOCX found, checking saved templates...');
      
      // STEP 2: Check if this is a saved template (JSON-based)
      try {
        const savedTemplatesPath = join(storageRoot, 'saved-templates.json');
        const savedTemplatesData = await readFile(savedTemplatesPath, 'utf-8');
        const savedTemplates = JSON.parse(savedTemplatesData);

        const savedTemplate = savedTemplates.find((t: any) => t.id === templateId);

        if (!savedTemplate) {
          console.log('❌ TEMPLATE PREVIEW: Template not found in any storage system');
          return NextResponse.json({ error: 'Template not found' }, { status: 404 });
        }

        console.log('✅ TEMPLATE PREVIEW: Found saved template:', savedTemplate.name);

        // STEP 2A: If saved template has originalTemplateId, show the original DOCX
        if (savedTemplate.originalTemplateId) {
          console.log('🔗 TEMPLATE PREVIEW: Saved template references original template:', savedTemplate.originalTemplateId);

          const originalDocxPath = join(uploadsDir, `${savedTemplate.originalTemplateId}.docx`);

          try {
            const originalDocxBuffer = await readFile(originalDocxPath);
            console.log('✅ TEMPLATE PREVIEW: Found original DOCX for saved template, size:', originalDocxBuffer.length, 'bytes');

            // Get filename from original template metadata
            let filename = `template-${savedTemplate.originalTemplateId}.docx`;
            try {
              const originalMetadataPath = join(uploadsDir, `${savedTemplate.originalTemplateId}.json`);
              const originalMetadata = JSON.parse(await readFile(originalMetadataPath, 'utf-8'));
              filename = originalMetadata.name || filename;
            } catch (e) {
              // Use saved template name as fallback
              filename = `${savedTemplate.name}.docx`;
            }

            return new NextResponse(originalDocxBuffer, {
              headers: {
                'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'Content-Disposition': `inline; filename="${filename}"`,
                'Content-Length': originalDocxBuffer.length.toString(),
              },
            });

          } catch (originalDocxError) {
            console.warn('⚠️ TEMPLATE PREVIEW: Original DOCX not found for saved template, falling back to markdown conversion');
            // Fall through to markdown conversion below
          }
        }

        // STEP 2B: Fall back to markdown conversion (for legacy saved templates or when original DOCX is missing)
        console.log('🔄 TEMPLATE PREVIEW: Generating DOCX from markdown...');

        if (!savedTemplate.markdown) {
          throw new Error('Saved template has no markdown content and no original template reference');
        }

        // Generate DOCX from markdown with better formatting
        const tempDir = join(storageRoot, 'temp');
        const tempMarkdownPath = join(tempDir, `${templateId}-${Date.now()}.md`);
        const tempDocxPath = join(tempDir, `${templateId}-${Date.now()}.docx`);

        // Ensure temp directory exists
        await mkdir(tempDir, { recursive: true });

        // Write markdown to temp file
        await writeFile(tempMarkdownPath, savedTemplate.markdown, 'utf-8');

        // Convert with better formatting options
        const pandocCommand = `pandoc "${tempMarkdownPath}" -f markdown -t docx --wrap=preserve --preserve-tabs --standalone -o "${tempDocxPath}"`;
        await execAsync(pandocCommand);

        // Read the generated DOCX
        const docxBuffer = await readFile(tempDocxPath);

        // Clean up temp files
        try {
          await unlink(tempMarkdownPath);
          await unlink(tempDocxPath);
        } catch (cleanupError) {
          console.warn('⚠️ TEMPLATE PREVIEW: Cleanup warning:', cleanupError);
        }

        console.log('✅ TEMPLATE PREVIEW: Generated DOCX, size:', docxBuffer.length, 'bytes');

        return new NextResponse(docxBuffer, {
          headers: {
            'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'Content-Disposition': `inline; filename="${savedTemplate.name || templateId}.docx"`,
            'Content-Length': docxBuffer.length.toString(),
          },
        });

      } catch (savedTemplateError) {
        console.error('❌ TEMPLATE PREVIEW: Saved template processing failed:', savedTemplateError);
        return NextResponse.json(
          { error: 'Failed to process template' },
          { status: 500 }
        );
      }
    }

  } catch (error) {
    console.error('❌ TEMPLATE PREVIEW: Unexpected error:', error);
    return NextResponse.json(
      { error: 'Failed to load template preview: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}
