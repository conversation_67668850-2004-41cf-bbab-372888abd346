import { NextRequest, NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join } from 'path';

// GET - Serve the original DOCX file for preview
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const resolvedParams = await params;
  try {
    const templateId = resolvedParams.id;

    console.log('🔍 DOCX PREVIEW: === STARTING TEMPLATE LOOKUP ===');
    console.log('🔍 DOCX PREVIEW: Template ID:', templateId);
    console.log('🔍 DOCX PREVIEW: Request URL:', request.url);
    console.log('🔍 DOCX PREVIEW: STORAGE_PATH env:', process.env.STORAGE_PATH || 'NOT SET');
    console.log('🔍 DOCX PREVIEW: Current working directory:', process.cwd());

    if (!templateId) {
      console.error('❌ DOCX PREVIEW: No template ID provided');
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    // FIXED: Check saved templates first - these don't have DOCX files, only markdown
    try {
      const savedTemplatesPath = join(process.cwd(), 'data', 'templates.json');
      console.log('🔍 DOCX PREVIEW: Checking saved templates at:', savedTemplatesPath);

      const savedTemplatesData = await readFile(savedTemplatesPath, 'utf-8');
      const savedTemplates = JSON.parse(savedTemplatesData);
      console.log('🔍 DOCX PREVIEW: Found', savedTemplates.length, 'saved templates');
      console.log('🔍 DOCX PREVIEW: Saved template IDs:', savedTemplates.map((t: any) => t.id));

      const savedTemplate = savedTemplates.find((t: any) => t.id === templateId);

      if (savedTemplate) {
        console.log('⚠️ DOCX PREVIEW: Found saved template, but no DOCX file available:', savedTemplate.name);
        return NextResponse.json(
          { error: 'DOCX preview not available for saved templates. Saved templates only contain markdown content.' },
          { status: 404 }
        );
      } else {
        console.log('🔍 DOCX PREVIEW: Template ID not found in saved templates');
      }
    } catch (savedTemplateError) {
      console.log('⚠️ DOCX PREVIEW: Error reading saved templates:', savedTemplateError);
    }

    // Fallback: Try uploaded templates system
    const uploadsDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'uploads') : join(process.cwd(), 'uploads');
    const docxPath = join(uploadsDir, `${templateId}.docx`);

    // Also try the templates directory for uploaded DOCX files
    const templatesDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'templates') : join(process.cwd(), 'storage', 'templates');
    const templateDocxPath = join(templatesDir, `${templateId}.docx`);

    console.log('🔍 DOCX PREVIEW: Trying uploaded templates system');
    console.log('🔍 DOCX PREVIEW: Uploads directory:', uploadsDir);
    console.log('🔍 DOCX PREVIEW: Looking for DOCX file at:', docxPath);

    // Check if uploads directory exists and list contents
    try {
      const { access, constants, readdir } = await import('fs/promises');
      await access(uploadsDir, constants.F_OK);
      console.log('✅ DOCX PREVIEW: Uploads directory exists');

      const files = await readdir(uploadsDir);
      console.log('🔍 DOCX PREVIEW: Files in uploads directory:', files);
      console.log('🔍 DOCX PREVIEW: Looking for file:', `${templateId}.docx`);
    } catch (dirError) {
      console.log('❌ DOCX PREVIEW: Uploads directory error:', dirError);
    }

    // Also check storage/templates directory (FIXED: Don't double up storage path)
    const storageTemplatesDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'templates') : join(process.cwd(), 'storage', 'templates');
    console.log('🔍 DOCX PREVIEW: Also checking storage/templates directory:', storageTemplatesDir);
    try {
      const { access, constants, readdir } = await import('fs/promises');
      await access(storageTemplatesDir, constants.F_OK);
      const storageFiles = await readdir(storageTemplatesDir);
      console.log('🔍 DOCX PREVIEW: Files in storage/templates:', storageFiles);
    } catch (storageError) {
      console.log('❌ DOCX PREVIEW: Storage/templates directory error:', storageError);
    }

    try {
      // Try reading from uploads directory first
      console.log('🔍 DOCX PREVIEW: Attempting to read DOCX file from uploads...');
      let docxBuffer: Buffer;
      try {
        docxBuffer = await readFile(docxPath);
        console.log('✅ DOCX PREVIEW: Successfully read DOCX file from uploads, size:', docxBuffer.length, 'bytes');
      } catch (uploadsError) {
        console.log('⚠️ DOCX PREVIEW: Failed to read from uploads, trying templates directory...');
        console.log('🔍 DOCX PREVIEW: Trying templates path:', templateDocxPath);
        docxBuffer = await readFile(templateDocxPath);
        console.log('✅ DOCX PREVIEW: Successfully read DOCX file from templates, size:', docxBuffer.length, 'bytes');
      }

      // Load template metadata for filename
      let filename = `template-${templateId}.docx`;
      try {
        const metadataPath = join(uploadsDir, `${templateId}.json`);
        const metadataContent = await readFile(metadataPath, 'utf-8');
        const metadata = JSON.parse(metadataContent);
        filename = metadata.name || filename;
      } catch (error) {
        console.warn('Could not load template metadata:', error);
      }

      // Return the DOCX file for preview/download
      return new NextResponse(docxBuffer, {
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'Content-Disposition': `inline; filename="${filename}"`,
          'Content-Length': docxBuffer.length.toString(),
        },
      });

    } catch (error) {
      console.error('❌ DOCX PREVIEW: Template file not found at:', docxPath);
      return NextResponse.json(
        { error: 'Template file not found' },
        { status: 404 }
      );
    }

  } catch (error) {
    console.error('Template preview error:', error);
    return NextResponse.json(
      { error: 'Failed to load template preview: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}
