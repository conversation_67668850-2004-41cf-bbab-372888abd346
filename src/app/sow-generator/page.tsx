'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import LoadingSpinner from '../components/loading-spinner';
import { useAutosave, useSavedUserData, useAutosaveStatus } from '../../hooks/useAutosave';
import { useNotification } from '../../contexts/NotificationContext';
import { UserDataService } from '../../lib/services/userDataService';
import { SavedUserData } from '../../lib/types/template';
import AutosaveIndicator from '../../components/ui/autosave-indicator';


interface UploadedTemplate {
  id: string;
  name: string;
  markdown: string;
  fields: string[];
}

interface ClientInfo {
  // Enhanced Client Contact Information
  clientName: string;
  clientEmail: string;
  clientCompany: string;
  clientPhone: string;
  clientAddress: string;
  clientTitle: string;
  clientDepartment: string;

  // Project Basics
  projectName: string;
  projectType: string;

  // Timeline
  startDate: string;
  endDate: string;
  duration: string;

  // Enhanced Pricing & Payment Schedule
  hourlyRate: string;
  estimatedHours: string;
  totalBudget: string;

  // Payment Schedule Details
  paymentSchedule: PaymentInstallment[];
  depositAmount: string;
  depositDueDate: string;
  finalPaymentAmount: string;
  finalPaymentDueDate: string;

  // User Information from Settings
  userCompanyName: string;
  userContactName: string;
  userEmail: string;
  userPhone: string;
  userAddress: string;
  userTitle: string;
  userDepartment: string;

  // Project Details
  projectDescription: string;
  deliverables: string[];
  requirements: string[];
  milestones: string[];
  additionalRequirements?: string;

  // Standard terms and processes
  changeRequestProcess: string;
  communicationPlan: string;
  qualityAssurance: string;
  supportAndMaintenance: string;
  intellectualProperty: string;
  confidentiality: string;
  terminationClause: string;
}

// Collapsible Category Component
const CollapsibleCategory: React.FC<{
  title: string;
  category?: 'clientInfo' | 'timeline' | 'budget' | 'userInfo' | 'projectDetails';
  children: React.ReactNode;
  icon?: React.ReactNode;
  isCollapsed: boolean;
  onToggle: () => void;
  id?: string;
}> = ({ title, category, children, icon, isCollapsed, onToggle, id }) => {
  return (
    <div className="mb-6 relative" style={{ zIndex: 1 }}>
      <button
        onClick={onToggle}
        className="w-full flex items-center justify-between p-4 bg-white/5 hover:bg-white/10 border border-white/10 rounded-xl transition-all duration-300 mb-4 relative"
        style={{ zIndex: 2 }}
      >
        <div className="flex items-center space-x-3">
          {icon}
          <h4 className="text-white font-semibold text-lg">{title}</h4>
        </div>
        <svg
          className={`w-5 h-5 text-white transition-transform duration-300 ${isCollapsed ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      <div className={`transition-all duration-300 relative ${isCollapsed ? 'max-h-0 opacity-0 overflow-hidden' : 'max-h-none opacity-100 overflow-visible'}`} style={{ zIndex: 0 }}>
        <div className="space-y-4">
          {children}
        </div>
      </div>
    </div>
  );
};

interface PaymentInstallment {
  id: string;
  description: string;
  amount: string;
  dueDate: string;
  milestone?: string;
}

const SOWGeneratorPage = () => {
  const [step, setStep] = useState<'upload' | 'info' | 'processing' | 'docx-review' | 'missing-info' | 'final-processing' | 'preview'>('upload');
  const [template, setTemplate] = useState<UploadedTemplate | null>(null);
  // Smart form state with auto-detection
  const [formData, setFormData] = useState<ClientInfo>({
    // Enhanced Client Contact Information
    clientName: '',
    clientEmail: '',
    clientCompany: '',
    clientPhone: '',
    clientAddress: '',
    clientTitle: '',
    clientDepartment: '',

    // Project Basics (simple inputs)
    projectName: '',
    projectType: '',

    // Timeline (smart defaults)
    startDate: '',
    endDate: '',
    duration: '4-6 weeks',

    // Enhanced Pricing & Payment Schedule
    hourlyRate: '',
    estimatedHours: '',
    totalBudget: '',

    // User Information from Settings
    userCompanyName: '',
    userContactName: '',
    userEmail: '',
    userPhone: '',
    userAddress: '',
    userTitle: '',
    userDepartment: '',


    // Payment Schedule Details
    paymentSchedule: [],
    depositAmount: '',
    depositDueDate: '',
    finalPaymentAmount: '',
    finalPaymentDueDate: '',

    // Project Details (the only complex field)
    projectDescription: '',
    deliverables: [],
    requirements: [],
    milestones: [],

    // Standard terms and processes (initialized as empty)
    changeRequestProcess: '',
    communicationPlan: '',
    qualityAssurance: '',
    supportAndMaintenance: '',
    intellectualProperty: '',
    confidentiality: '',
    terminationClause: ''
  });

  const [currentStep, setCurrentStep] = useState(1);
  const [autoSuggestions, setAutoSuggestions] = useState({
    projectTypes: ['Website Development', 'Mobile App', 'Consulting', 'Design', 'Marketing Campaign'],
    durations: ['2-3 weeks', '4-6 weeks', '2-3 months', '3-6 months'],
    deliverables: ['Website', 'Mobile App', 'Logo Design', 'Marketing Strategy', 'Consultation Report'],

    milestones: ['Project kickoff', 'Design approval', 'Development completion', 'Testing', 'Final delivery'],
    projectPhases: ['Discovery & Planning', 'Design Phase', 'Development Phase', 'Testing Phase', 'Deployment Phase'],
    clientTitles: ['CEO', 'CTO', 'Marketing Director', 'Project Manager', 'Operations Manager', 'Founder'],
    departments: ['Marketing', 'IT', 'Operations', 'Sales', 'Executive', 'Product Development'],
    paymentScheduleTemplates: [
      { name: '50/50 Split', installments: [{ description: 'Project Start', percentage: 50 }, { description: 'Project Completion', percentage: 50 }] },
      { name: '3-Payment Plan', installments: [{ description: 'Project Start', percentage: 40 }, { description: 'Milestone 1', percentage: 30 }, { description: 'Final Delivery', percentage: 30 }] },
      { name: 'Monthly Billing', installments: [{ description: 'Month 1', percentage: 25 }, { description: 'Month 2', percentage: 25 }, { description: 'Month 3', percentage: 25 }, { description: 'Month 4', percentage: 25 }] }
    ]
  });

  // Collapsible categories state - Start collapsed for clean look
  const [collapsedCategories, setCollapsedCategories] = useState({
    clientInfo: true,
    timeline: true,
    budget: true,
    userInfo: true,
    projectDetails: true,
    'advanced-options': true,
    // Smart Analysis categories - Start collapsed for cleaner interface
    analysis_ClientInformation: true,
    analysis_YourInformation: true,
    analysis_ProjectDetails: true,
    analysis_TimelineBudget: true,
    analysis_DeliverablesRequirements: true,
    analysis_Other: true
  });

  const [detectedFields, setDetectedFields] = useState<string[]>([]);
  const [processedMarkdown, setProcessedMarkdown] = useState<string>('');
  const [generatedDocxBuffer, setGeneratedDocxBuffer] = useState<ArrayBuffer | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [availableTemplates, setAvailableTemplates] = useState<any[]>([]);
  const [showTemplateSelector, setShowTemplateSelector] = useState(false);
  const [customSOWName, setCustomSOWName] = useState('');
  const [showNameEditor, setShowNameEditor] = useState(false);

  // Review step state
  const [missingInfo, setMissingInfo] = useState<Array<{
    id: string;
    title: string;
    description: string;
    priority: 'high' | 'medium' | 'low';
    completed: boolean;
    category: string;
    examples?: string;
    userInput?: string;
    skipped?: boolean;
  }>>([]);
  const [reviewContent, setReviewContent] = useState('');
  const [additionalInfo, setAdditionalInfo] = useState('');
  const [completionScore, setCompletionScore] = useState(0);

  // Step-by-step missing info flow
  const [currentMissingInfoStep, setCurrentMissingInfoStep] = useState(0);
  const [liveSOWPreview, setLiveSOWPreview] = useState('');

  // Template saving option
  const [saveAsTemplate, setSaveAsTemplate] = useState(false);

  // Template analysis state
  const [templateAnalysis, setTemplateAnalysis] = useState<any>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [staticVariables, setStaticVariables] = useState<any>(null);

  // New workflow state
  const [initialProcessedMarkdown, setInitialProcessedMarkdown] = useState<string>('');
  const [currentDocxBuffer, setCurrentDocxBuffer] = useState<ArrayBuffer | null>(null);
  const [documentSummary, setDocumentSummary] = useState<string>('');
  const [isConvertingDocx, setIsConvertingDocx] = useState(false);

  // AI Prompt editing state
  const [customPrompt, setCustomPrompt] = useState<string>('');
  const [useCustomPrompt, setUseCustomPrompt] = useState(false);

  // Custom dropdown values state
  const [customValues, setCustomValues] = useState({
    clientTitle: '',
    clientDepartment: '',
    projectType: '',
    duration: '',
    userTitle: '',
    userDepartment: ''
  });
  const [showCustomInputs, setShowCustomInputs] = useState({
    clientTitle: false,
    clientDepartment: false,
    projectType: false,
    duration: false,
    userTitle: false,
    userDepartment: false
  });

  // Template management state
  const [selectedTemplates, setSelectedTemplates] = useState<Set<string>>(new Set());
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [previewTemplateId, setPreviewTemplateId] = useState<string | null>(null);

  const router = useRouter();
  const { showNotification } = useNotification();

  // Toggle collapsible category
  const toggleCategory = (category: string) => {
    setCollapsedCategories(prev => ({
      ...prev,
      [category]: !prev[category as keyof typeof prev]
    }));
  };

  // Helper function to safely format budget values
  const formatBudget = (value: string | number): string => {
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    return (numValue && !isNaN(numValue) && numValue > 0) ? numValue.toLocaleString() : '0';
  };

  // Convert formData to SavedUserData format for autosave
  const convertFormDataToSavedUserData = (): SavedUserData => ({
    clientName: formData.clientName,
    clientEmail: formData.clientEmail,
    clientCompany: formData.clientCompany,
    clientPhone: formData.clientPhone,
    clientAddress: formData.clientAddress,
    clientTitle: formData.clientTitle,
    clientDepartment: formData.clientDepartment,
    projectName: formData.projectName,
    projectType: formData.projectType,
    projectDescription: formData.projectDescription,
    startDate: formData.startDate,
    endDate: formData.endDate,
    duration: formData.duration,
    hourlyRate: formData.hourlyRate,
    estimatedHours: formData.estimatedHours,
    totalBudget: formData.totalBudget,
    paymentSchedule: formData.paymentSchedule.map(p => ({
      id: p.id,
      description: p.description,
      amount: p.amount,
      dueDate: p.dueDate
    })),
    customFields: {
      deliverables: formData.deliverables,
      requirements: formData.requirements,
      milestones: formData.milestones,
      additionalRequirements: formData.additionalRequirements,
      changeRequestProcess: formData.changeRequestProcess,
      communicationPlan: formData.communicationPlan,
      qualityAssurance: formData.qualityAssurance,
      supportAndMaintenance: formData.supportAndMaintenance,
      intellectualProperty: formData.intellectualProperty,
      confidentiality: formData.confidentiality,
      terminationClause: formData.terminationClause,
      // Include user information fields
      userCompanyName: formData.userCompanyName,
      userContactName: formData.userContactName,
      userEmail: formData.userEmail,
      userPhone: formData.userPhone,
      userAddress: formData.userAddress,
      userTitle: formData.userTitle,
      userDepartment: formData.userDepartment
    }
  });

  // Autosave functionality with faster interval
  const { isSaving, lastSaved, saveNow, hasUnsavedChanges } = useAutosave({
    templateId: template?.id || '',
    data: convertFormDataToSavedUserData(),
    enabled: !!template?.id && (step === 'info' || step === 'missing-info'),
    interval: 1500, // 1.5 seconds for faster saving
    onSave: (data) => {
      console.log('✅ Autosaved user data:', {
        templateId: template?.id,
        hasClientName: !!data.clientName,
        hasProjectName: !!data.projectName,
        hasUserInfo: !!(data.customFields?.userCompanyName || data.customFields?.userContactName),
        totalFields: Object.keys(data).length
      });
    },
    onError: (error) => {
      console.error('❌ Autosave failed:', error);
    }
  });

  // Load saved user data when template is selected (only for automatic loading via hook)
  const { savedData, isLoading: isLoadingSavedData } = useSavedUserData(template?.id || '');

  // Note: Saved data loading is now handled in loadTemplateById function when user explicitly chooses to load saved data

  // Smart auto-fill functions
  const autoFillDefaults = () => {
    const today = new Date();
    const nextMonth = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000);

    setFormData(prev => ({
      ...prev,
      startDate: prev.startDate || today.toISOString().split('T')[0],
      endDate: prev.endDate || nextMonth.toISOString().split('T')[0],
      hourlyRate: prev.hourlyRate || '150', // Default rate
    }));
  };

  const calculateBudget = useCallback((hours?: string, rate?: string) => {
    // Use provided values or current form data
    const hoursValue = parseFloat(hours || formData.estimatedHours) || 0;
    const rateValue = parseFloat(rate || formData.hourlyRate) || 0;
    const total = Math.round(hoursValue * rateValue);

    // Ensure we never set NaN or negative values
    const finalTotal = isNaN(total) || total < 0 ? 0 : total;

    console.log('💰 BUDGET CALCULATION:', {
      hours: hoursValue,
      rate: rateValue,
      total: finalTotal,
      formatted: finalTotal.toLocaleString()
    });

    setFormData(prev => ({
      ...prev,
      totalBudget: finalTotal.toString()
    }));

    return finalTotal;
  }, [formData.estimatedHours, formData.hourlyRate]);

  const addDeliverable = (deliverable: string) => {
    if (!formData.deliverables.includes(deliverable)) {
      setFormData(prev => ({
        ...prev,
        deliverables: [...prev.deliverables, deliverable]
      }));
    }
  };

  const removeDeliverable = (index: number) => {
    setFormData(prev => ({
      ...prev,
      deliverables: prev.deliverables.filter((_, i) => i !== index)
    }));
  };

  // Payment schedule management
  const addPaymentInstallment = () => {
    const newInstallment: PaymentInstallment = {
      id: Date.now().toString(),
      description: '',
      amount: '',
      dueDate: '',
      milestone: ''
    };
    setFormData(prev => ({
      ...prev,
      paymentSchedule: [...prev.paymentSchedule, newInstallment]
    }));
  };

  const updatePaymentInstallment = (id: string, field: keyof PaymentInstallment, value: string) => {
    setFormData(prev => ({
      ...prev,
      paymentSchedule: prev.paymentSchedule.map(installment =>
        installment.id === id ? { ...installment, [field]: value } : installment
      )
    }));
  };

  const removePaymentInstallment = (id: string) => {
    setFormData(prev => ({
      ...prev,
      paymentSchedule: prev.paymentSchedule.filter(installment => installment.id !== id)
    }));
  };

  const applyPaymentTemplate = (template: any) => {
    const totalBudget = parseFloat(formData.totalBudget) || 0;
    const installments = template.installments.map((inst: any, index: number) => ({
      id: `template_${Date.now()}_${index}`,
      description: inst.description,
      amount: (totalBudget * (inst.percentage / 100)).toFixed(2),
      dueDate: '',
      milestone: inst.description
    }));

    setFormData(prev => ({
      ...prev,
      paymentSchedule: installments
    }));
  };



  const buildSmartPrompt = () => {
    // If using custom prompt, return it directly
    if (useCustomPrompt && customPrompt.trim()) {
      return customPrompt;
    }

    const parts = [];

    // Your Information (Service Provider)
    if (formData.userCompanyName) parts.push(`Service Provider: ${formData.userCompanyName}`);
    if (formData.userContactName) parts.push(`Contact: ${formData.userContactName}`);
    if (formData.userTitle) parts.push(`Provider Title: ${formData.userTitle}`);
    if (formData.userDepartment) parts.push(`Provider Department: ${formData.userDepartment}`);
    if (formData.userEmail) parts.push(`Provider Email: ${formData.userEmail}`);
    if (formData.userPhone) parts.push(`Provider Phone: ${formData.userPhone}`);
    if (formData.userAddress) parts.push(`Provider Address: ${formData.userAddress}`);

    // Enhanced Client Information
    if (formData.clientName) parts.push(`Client: ${formData.clientName}`);
    if (formData.clientTitle) parts.push(`Title: ${formData.clientTitle}`);
    if (formData.clientCompany) parts.push(`Company: ${formData.clientCompany}`);
    if (formData.clientDepartment) parts.push(`Department: ${formData.clientDepartment}`);
    if (formData.clientEmail) parts.push(`Email: ${formData.clientEmail}`);
    if (formData.clientPhone) parts.push(`Phone: ${formData.clientPhone}`);
    if (formData.clientAddress) parts.push(`Address: ${formData.clientAddress}`);

    // Project Basics
    if (formData.projectName) parts.push(`Project: ${formData.projectName}`);
    if (formData.projectType) parts.push(`Type: ${formData.projectType}`);

    // Timeline
    if (formData.startDate) parts.push(`Start Date: ${formData.startDate}`);
    if (formData.endDate) parts.push(`End Date: ${formData.endDate}`);
    if (formData.duration) parts.push(`Duration: ${formData.duration}`);

    // Pricing & Payment Schedule
    if (formData.hourlyRate) parts.push(`Rate: $${formData.hourlyRate}/hour`);
    if (formData.estimatedHours) parts.push(`Estimated Hours: ${formData.estimatedHours}`);
    if (formData.totalBudget) parts.push(`Total Budget: $${formData.totalBudget}`);


    // Payment Schedule Details
    if (formData.depositAmount) parts.push(`Deposit: ${formData.depositAmount} (Due: ${formData.depositDueDate || 'TBD'})`);
    if (formData.finalPaymentAmount) parts.push(`Final Payment: ${formData.finalPaymentAmount} (Due: ${formData.finalPaymentDueDate || 'TBD'})`);

    if (formData.paymentSchedule.length > 0) {
      const scheduleDetails = formData.paymentSchedule.map(installment =>
        `${installment.description}: ${installment.amount} (Due: ${installment.dueDate || 'TBD'})`
      ).join(', ');
      parts.push(`Payment Schedule: ${scheduleDetails}`);
    }

    // Deliverables
    if (formData.deliverables.length > 0) {
      parts.push(`Deliverables: ${formData.deliverables.join(', ')}`);
    }

    // Project Description (the main content)
    if (formData.projectDescription) {
      parts.push(`\nProject Description:\n${formData.projectDescription}`);
    }

    // Additional Requirements
    if (formData.additionalRequirements) {
      parts.push(`\nAdditional Requirements:\n${formData.additionalRequirements}`);
    }

    return parts.join('\n');
  };

  // Function to generate the current prompt preview
  const generatePromptPreview = () => {
    return buildSmartPrompt();
  };

  // Handle custom dropdown selection
  const handleDropdownChange = (field: keyof typeof customValues, value: string) => {
    if (value === 'custom') {
      setShowCustomInputs(prev => ({ ...prev, [field]: true }));
    } else {
      setShowCustomInputs(prev => ({ ...prev, [field]: false }));
      setFormData(prev => ({ ...prev, [field]: value }));
    }
  };

  // Handle custom input submission
  const handleCustomInputSubmit = (field: keyof typeof customValues) => {
    const customValue = customValues[field];
    if (customValue.trim()) {
      setFormData(prev => ({ ...prev, [field]: customValue }));
      setShowCustomInputs(prev => ({ ...prev, [field]: false }));
      setCustomValues(prev => ({ ...prev, [field]: '' }));

      // Add the custom value to the appropriate suggestions array
      setAutoSuggestions(prev => {
        const newSuggestions = { ...prev };
        switch (field) {
          case 'clientTitle':
          case 'userTitle':
            if (!newSuggestions.clientTitles.includes(customValue)) {
              newSuggestions.clientTitles = [...newSuggestions.clientTitles, customValue];
            }
            break;
          case 'clientDepartment':
          case 'userDepartment':
            if (!newSuggestions.departments.includes(customValue)) {
              newSuggestions.departments = [...newSuggestions.departments, customValue];
            }
            break;
          case 'projectType':
            if (!newSuggestions.projectTypes.includes(customValue)) {
              newSuggestions.projectTypes = [...newSuggestions.projectTypes, customValue];
            }
            break;
          case 'duration':
            if (!newSuggestions.durations.includes(customValue)) {
              newSuggestions.durations = [...newSuggestions.durations, customValue];
            }
            break;
        }
        return newSuggestions;
      });
    }
  };

  // Handle custom input cancel
  const handleCustomInputCancel = (field: keyof typeof customValues) => {
    setShowCustomInputs(prev => ({ ...prev, [field]: false }));
    setCustomValues(prev => ({ ...prev, [field]: '' }));
  };

  // Auto-calculate duration from start and end dates
  const calculateDuration = (startDate: string, endDate: string): string => {
    if (!startDate || !endDate || endDate === 'TBD') {
      return 'TBD';
    }

    try {
      const start = new Date(startDate);
      const end = new Date(endDate);

      if (start >= end) {
        return 'Invalid dates';
      }

      const diffTime = Math.abs(end.getTime() - start.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      const diffWeeks = Math.ceil(diffDays / 7);
      const diffMonths = Math.ceil(diffDays / 30);

      if (diffDays <= 7) {
        return `${diffDays} day${diffDays === 1 ? '' : 's'}`;
      } else if (diffWeeks <= 8) {
        return `${diffWeeks} week${diffWeeks === 1 ? '' : 's'}`;
      } else if (diffMonths <= 6) {
        return `${diffMonths} month${diffMonths === 1 ? '' : 's'}`;
      } else {
        return `${Math.ceil(diffMonths / 12)} year${Math.ceil(diffMonths / 12) === 1 ? '' : 's'}`;
      }
    } catch (error) {
      return 'Invalid dates';
    }
  };

  // Auto-update duration when dates change
  useEffect(() => {
    if (formData.startDate && formData.endDate) {
      const calculatedDuration = calculateDuration(formData.startDate, formData.endDate);
      if (calculatedDuration !== formData.duration) {
        setFormData(prev => ({ ...prev, duration: calculatedDuration }));
      }
    }
  }, [formData.startDate, formData.endDate, formData.duration]);

  // Auto-resize textarea to fit content
  const autoResizeTextarea = (element: HTMLTextAreaElement) => {
    element.style.height = 'auto';
    element.style.height = Math.max(element.scrollHeight, 80) + 'px'; // Minimum height of 80px
  };

  // Handle address change with suggestions
  const handleAddressChange = (value: string) => {
    setFormData(prev => ({ ...prev, clientAddress: value }));

    if (value.length >= 3) {
      const suggestions = generateAddressSuggestions(value);
      setAddressSuggestions(suggestions);
      setShowAddressSuggestions(suggestions.length > 0);
    } else {
      setShowAddressSuggestions(false);
    }
  };

  // Select address suggestion
  const selectAddressSuggestion = (suggestion: string, event?: React.MouseEvent) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    setFormData(prev => ({ ...prev, clientAddress: suggestion }));
    setShowAddressSuggestions(false);
  };

  // Handle user address change with suggestions
  const handleUserAddressChange = (value: string) => {
    setFormData(prev => ({ ...prev, userAddress: value }));

    if (value.length >= 3) {
      const suggestions = generateAddressSuggestions(value);
      setUserAddressSuggestions(suggestions);
      setShowUserAddressSuggestions(suggestions.length > 0);
    } else {
      setShowUserAddressSuggestions(false);
    }
  };

  // Select user address suggestion
  const selectUserAddressSuggestion = (suggestion: string, event?: React.MouseEvent) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    setFormData(prev => ({ ...prev, userAddress: suggestion }));
    setShowUserAddressSuggestions(false);
  };

  // Auto-distribute budget across payment schedule
  const autoDistributeBudget = (template: string) => {
    const totalBudget = parseFloat(formData.totalBudget) || 0;
    if (totalBudget === 0) return;

    let installments: PaymentInstallment[] = [];

    switch (template) {
      case '50/50':
        installments = [
          {
            id: `auto_${Date.now()}_1`,
            description: 'Project Start (50%)',
            amount: (totalBudget * 0.5).toFixed(2),
            dueDate: '',
            milestone: 'Project Start'
          },
          {
            id: `auto_${Date.now()}_2`,
            description: 'Project Completion (50%)',
            amount: (totalBudget * 0.5).toFixed(2),
            dueDate: '',
            milestone: 'Project Completion'
          }
        ];
        break;
      case '3-payment':
        installments = [
          {
            id: `auto_${Date.now()}_1`,
            description: 'Project Start (40%)',
            amount: (totalBudget * 0.4).toFixed(2),
            dueDate: '',
            milestone: 'Project Start'
          },
          {
            id: `auto_${Date.now()}_2`,
            description: 'Milestone 1 (30%)',
            amount: (totalBudget * 0.3).toFixed(2),
            dueDate: '',
            milestone: 'Milestone 1'
          },
          {
            id: `auto_${Date.now()}_3`,
            description: 'Final Delivery (30%)',
            amount: (totalBudget * 0.3).toFixed(2),
            dueDate: '',
            milestone: 'Final Delivery'
          }
        ];
        break;
      case 'monthly':
        installments = [
          {
            id: `auto_${Date.now()}_1`,
            description: 'Month 1 (25%)',
            amount: (totalBudget * 0.25).toFixed(2),
            dueDate: '',
            milestone: 'Month 1'
          },
          {
            id: `auto_${Date.now()}_2`,
            description: 'Month 2 (25%)',
            amount: (totalBudget * 0.25).toFixed(2),
            dueDate: '',
            milestone: 'Month 2'
          },
          {
            id: `auto_${Date.now()}_3`,
            description: 'Month 3 (25%)',
            amount: (totalBudget * 0.25).toFixed(2),
            dueDate: '',
            milestone: 'Month 3'
          },
          {
            id: `auto_${Date.now()}_4`,
            description: 'Month 4 (25%)',
            amount: (totalBudget * 0.25).toFixed(2),
            dueDate: '',
            milestone: 'Month 4'
          }
        ];
        break;
    }

    setFormData(prev => ({
      ...prev,
      paymentSchedule: installments
    }));
  };

  // Address suggestions state
  const [addressSuggestions, setAddressSuggestions] = useState<string[]>([]);
  const [showAddressSuggestions, setShowAddressSuggestions] = useState(false);

  // User address suggestions state
  const [userAddressSuggestions, setUserAddressSuggestions] = useState<string[]>([]);
  const [showUserAddressSuggestions, setShowUserAddressSuggestions] = useState(false);

  // Common address patterns for intelligent autofill
  const generateAddressSuggestions = (input: string): string[] => {
    if (input.length < 3) return [];

    const suggestions: string[] = [];
    const inputLower = input.toLowerCase().trim();

    // Parse the input to understand what the user is typing
    const words = inputLower.split(/\s+/);
    const hasNumber = /\d/.test(input);
    const streetNumber = input.match(/^\d+/)?.[0] || '';

    // Common street types and their variations
    const streetTypes = {
      'st': ['Street', 'St'],
      'street': ['Street', 'St'],
      'ave': ['Avenue', 'Ave'],
      'avenue': ['Avenue', 'Ave'],
      'blvd': ['Boulevard', 'Blvd'],
      'boulevard': ['Boulevard', 'Blvd'],
      'dr': ['Drive', 'Dr'],
      'drive': ['Drive', 'Dr'],
      'rd': ['Road', 'Rd'],
      'road': ['Road', 'Rd'],
      'ln': ['Lane', 'Ln'],
      'lane': ['Lane', 'Ln'],
      'way': ['Way'],
      'ct': ['Court', 'Ct'],
      'court': ['Court', 'Ct'],
      'pl': ['Place', 'Pl'],
      'place': ['Place', 'Pl']
    };

    // Directional indicators
    const directions = ['n', 'north', 's', 'south', 'e', 'east', 'w', 'west', 'ne', 'nw', 'se', 'sw'];

    // Major cities with common street names
    const cityStreetPatterns = [
      // Minneapolis/St. Paul area
      { city: 'Minneapolis, MN', streets: ['Delaware', 'University', 'Washington', 'Hennepin', 'Nicollet', 'Lyndale', 'Franklin', 'Lake'] },
      { city: 'St. Paul, MN', streets: ['Grand', 'Summit', 'University', 'Selby', 'Marshall', 'Randolph'] },
      // Other major cities
      { city: 'New York, NY', streets: ['Broadway', 'Madison', 'Park', 'Lexington', 'Fifth', '42nd', 'Wall'] },
      { city: 'Los Angeles, CA', streets: ['Sunset', 'Hollywood', 'Melrose', 'Wilshire', 'Santa Monica', 'Ventura'] },
      { city: 'Chicago, IL', streets: ['Michigan', 'State', 'LaSalle', 'Wabash', 'Clark', 'Dearborn'] },
      { city: 'San Francisco, CA', streets: ['Market', 'Mission', 'Castro', 'Lombard', 'Powell', 'Union'] }
    ];

    // Try to match and complete the address
    if (hasNumber && words.length >= 2) {
      const streetName = words.slice(1).join(' ');

      // Look for matching street names in our database
      cityStreetPatterns.forEach(cityData => {
        cityData.streets.forEach(knownStreet => {
          if (knownStreet.toLowerCase().includes(streetName) || streetName.includes(knownStreet.toLowerCase())) {
            // Generate variations with different street types
            Object.values(streetTypes).forEach(typeVariations => {
              typeVariations.forEach(streetType => {
                // Try different directional combinations
                directions.forEach(direction => {
                  if (streetName.includes(direction) || inputLower.includes(direction)) {
                    suggestions.push(`${streetNumber} ${knownStreet} ${streetType} ${direction.toUpperCase()}, ${cityData.city}`);
                  }
                });
                // Also add without direction
                suggestions.push(`${streetNumber} ${knownStreet} ${streetType}, ${cityData.city}`);
              });
            });
          }
        });
      });
    }

    // If we found specific matches, prioritize them
    if (suggestions.length > 0) {
      // Remove duplicates and sort by relevance
      const uniqueSuggestions = Array.from(new Set(suggestions));
      return uniqueSuggestions.slice(0, 3);
    }

    // Fallback: try to complete based on partial input
    if (hasNumber) {
      const partialStreet = words.slice(1).join(' ');

      // Common street name completions
      const commonStreets = ['Main', 'First', 'Second', 'Third', 'Oak', 'Elm', 'Maple', 'Park', 'Washington', 'Lincoln'];

      commonStreets.forEach(street => {
        if (street.toLowerCase().includes(partialStreet) || partialStreet.includes(street.toLowerCase())) {
          suggestions.push(
            `${streetNumber} ${street} Street, Minneapolis, MN 55401`,
            `${streetNumber} ${street} Avenue, St. Paul, MN 55102`,
            `${streetNumber} ${street} Boulevard, Chicago, IL 60601`
          );
        }
      });
    }

    // If still no matches, provide generic completions based on what they've typed
    if (suggestions.length === 0 && hasNumber) {
      const currentInput = input.trim();
      suggestions.push(
        `${currentInput}, Minneapolis, MN 55401`,
        `${currentInput}, St. Paul, MN 55102`,
        `${currentInput}, Chicago, IL 60601`
      );
    }

    return suggestions.slice(0, 3);
  };





  // Save current template with form data
  const saveTemplateWithFormData = async (templateName: string) => {
    if (!template) {
      showNotification({
        type: 'error',
        title: 'No Template',
        message: 'No template loaded to save'
      });
      return;
    }

    try {
      const response = await fetch('/api/templates/save', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: templateName,
          markdown: template.markdown,
          sections: [],
          formData: formData // Include all current form data
        }),
      });

      if (response.ok) {
        const result = await response.json();
        showNotification({
          type: 'success',
          title: 'Template Saved',
          message: `Template "${templateName}" saved successfully with your current information!`
        });
        console.log('✅ Template saved with form data:', result);
      } else {
        throw new Error('Failed to save template');
      }
    } catch (error) {
      console.error('❌ Failed to save template:', error);
      showNotification({
        type: 'error',
        title: 'Save Failed',
        message: 'Failed to save template. Please try again.'
      });
    }
  };

  // Load template with saved form data
  const loadTemplateWithFormData = async (templateId: string) => {
    try {
      console.log('🔄 Loading template with form data:', templateId);

      // First try to find the template in our available templates to determine type
      const templateInfo = availableTemplates.find(t => t.id === templateId);

      let response;
      if (templateInfo?.type === 'saved') {
        // Load from saved templates (file-based)
        response = await fetch(`/api/templates/${templateId}`);
      } else {
        // Load from uploaded templates (file-based)
        response = await fetch(`/api/template/preview?id=${templateId}`);
      }

      if (response.ok) {
        const templateData = await response.json();
        console.log('📄 Template data loaded:', templateData);

        // Validate template data
        if (!templateData.id || !templateData.name || !templateData.markdown) {
          throw new Error('Invalid template data received from server');
        }

        // Load the template
        setTemplate({
          id: templateData.id,
          name: templateData.name,
          markdown: templateData.markdown,
          fields: templateData.fields || templateData.sections || []
        });

        // If the template has saved form data, load it
        if (templateData.savedFormData) {
          setFormData(templateData.savedFormData);
          console.log('✅ Loaded template with saved form data');
          showNotification({
            type: 'success',
            title: 'Template Loaded',
            message: 'Template loaded with your previously saved information!'
          });
        } else {
          showNotification({
            type: 'info',
            title: 'Template Loaded',
            message: 'Template loaded successfully!'
          });
        }

        // FIXED: Navigate to info step after successful template loading
        setStep('info');
        console.log('🎉 COMPLETE: Template loaded with form data, navigating to info step!');

        return templateData;
      } else {
        const errorText = await response.text();
        console.error('❌ Template loading failed with status:', response.status, errorText);
        throw new Error(`Failed to load template: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.error('❌ Failed to load template:', error);

      // Provide more specific error messages based on error type
      let errorMessage = 'Failed to load template. Please try again.';
      if (error instanceof Error) {
        if (error.message.includes('404')) {
          errorMessage = 'Template not found. It may have been deleted or moved.';
        } else if (error.message.includes('500')) {
          errorMessage = 'Server error while loading template. Please try again in a moment.';
        } else if (error.message.includes('Invalid template data')) {
          errorMessage = 'Template data is corrupted. Please re-upload the template.';
        }
      }

      showNotification({
        type: 'error',
        title: 'Load Failed',
        message: errorMessage
      });
    }
  };



  // Template management functions
  const toggleTemplateSelection = (templateId: string) => {
    setSelectedTemplates(prev => {
      const newSet = new Set(prev);
      if (newSet.has(templateId)) {
        newSet.delete(templateId);
      } else {
        newSet.add(templateId);
      }
      return newSet;
    });
  };

  const handleBulkDelete = async () => {
    if (selectedTemplates.size === 0) return;

    const confirmed = confirm(`Are you sure you want to delete ${selectedTemplates.size} template(s)?`);
    if (!confirmed) return;

    try {
      console.log('🗑️ BULK DELETE: Starting bulk deletion of templates:', Array.from(selectedTemplates));
      console.log('🔍 BULK DELETE: Available templates for reference:', availableTemplates.map(t => ({ id: t.id, name: t.name, type: t.type })));

      // Process deletions sequentially to avoid race conditions and API rate limiting
      const results = [];
      let successCount = 0;
      let failureCount = 0;

      for (const templateId of Array.from(selectedTemplates)) {
        try {
          // Find the template to determine its type
          const templateInfo = availableTemplates.find(t => t.id === templateId);
          console.log('🔍 BULK DELETE: Processing template', templateId, ':', templateInfo);

          let response;
          let endpoint = '';

          if (templateInfo?.type === 'saved') {
            // Delete from saved templates (file-based)
            endpoint = `/api/templates/save?id=${templateId}`;
            console.log('🗑️ BULK DELETE: Deleting saved template via:', endpoint);
          } else if (templateInfo?.type === 'uploaded') {
            // Delete from uploaded templates (file-based)
            endpoint = `/api/template?id=${templateId}`;
            console.log('🗑️ BULK DELETE: Deleting uploaded template via:', endpoint);
          } else {
            // Try database templates as fallback
            endpoint = `/api/templates?id=${templateId}`;
            console.log('🗑️ BULK DELETE: Deleting database template via:', endpoint);
          }

          response = await fetch(endpoint, {
            method: 'DELETE'
          });

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
            console.error('❌ BULK DELETE: Failed to delete template', templateId, ':', errorData);
            results.push({ status: 'rejected', reason: `Failed to delete template ${templateId}: ${errorData.error}` });
            failureCount++;
          } else {
            console.log('✅ BULK DELETE: Successfully deleted template:', templateId);
            results.push({ status: 'fulfilled', value: response });
            successCount++;
          }

          // Add small delay between requests to prevent overwhelming the server
          await new Promise(resolve => setTimeout(resolve, 100));

        } catch (error) {
          console.error('❌ BULK DELETE: Error processing template', templateId, ':', error);
          results.push({ status: 'rejected', reason: `Error deleting template ${templateId}: ${error instanceof Error ? error.message : 'Unknown error'}` });
          failureCount++;
        }
      }

      console.log('📊 BULK DELETE: Final Results - Successes:', successCount, 'Failures:', failureCount);
      console.log('📋 BULK DELETE: Detailed results:', results);

      // Reload templates to reflect changes
      await loadAvailableTemplates();
      setSelectedTemplates(new Set());
      setIsSelectionMode(false);

      if (failureCount === 0) {
        showNotification({
          type: 'success',
          title: 'Templates Deleted',
          message: `Successfully deleted ${successCount} template(s)`
        });
      } else if (successCount > 0) {
        showNotification({
          type: 'warning',
          title: 'Partial Success',
          message: `Deleted ${successCount} template(s). ${failureCount} failed to delete.`
        });
      } else {
        showNotification({
          type: 'error',
          title: 'Delete Failed',
          message: `Failed to delete all ${failureCount} template(s). Please try again.`
        });
      }
    } catch (error) {
      console.error('❌ BULK DELETE: Failed to delete templates:', error);
      showNotification({
        type: 'error',
        title: 'Delete Failed',
        message: 'Failed to delete templates. Please try again.'
      });
    }
  };

  const handleDeleteTemplate = async (templateId: string) => {
    if (!confirm('Are you sure you want to delete this template?')) {
      return;
    }

    try {
      console.log('🗑️ SINGLE DELETE: Starting deletion of template:', templateId);

      // Find the template to determine its type
      const templateInfo = availableTemplates.find(t => t.id === templateId);
      console.log('🔍 SINGLE DELETE: Template info:', templateInfo);

      let response;
      let endpoint = '';

      if (templateInfo?.type === 'saved') {
        // Delete from saved templates (file-based)
        endpoint = `/api/templates/save?id=${templateId}`;
        console.log('🗑️ SINGLE DELETE: Deleting saved template via:', endpoint);
        response = await fetch(endpoint, {
          method: 'DELETE'
        });
      } else if (templateInfo?.type === 'uploaded') {
        // Delete from uploaded templates (file-based)
        endpoint = `/api/template?id=${templateId}`;
        console.log('🗑️ SINGLE DELETE: Deleting uploaded template via:', endpoint);
        response = await fetch(endpoint, {
          method: 'DELETE'
        });
      } else {
        // Try database templates as fallback
        endpoint = `/api/templates?id=${templateId}`;
        console.log('🗑️ SINGLE DELETE: Deleting database template via:', endpoint);
        response = await fetch(endpoint, {
          method: 'DELETE'
        });
      }

      console.log('📡 SINGLE DELETE: Response status:', response.status, 'OK:', response.ok);

      if (response.ok) {
        console.log('✅ SINGLE DELETE: Template deleted successfully');
        loadAvailableTemplates();
        showNotification({
          type: 'success',
          title: 'Template Deleted',
          message: 'Template deleted successfully'
        });
      } else {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        console.error('❌ SINGLE DELETE: Failed to delete template:', errorData);
        throw new Error(`Failed to delete template: ${errorData.error}`);
      }
    } catch (error) {
      console.error('❌ SINGLE DELETE: Error deleting template:', error);
      showNotification({
        type: 'error',
        title: 'Delete Failed',
        message: `Failed to delete template: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    }
  };

  const handlePreviewTemplate = (templateId: string) => {
    setPreviewTemplateId(templateId);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Handle missing info checkbox toggle
  const toggleMissingInfoItem = (id: string) => {
    setMissingInfo(prev =>
      prev.map(item =>
        item.id === id
          ? { ...item, completed: !item.completed }
          : item
      )
    );
  };

  // Calculate completion percentage
  const calculateCompletionPercentage = () => {
    if (missingInfo.length === 0) return 100;
    const processedItems = missingInfo.filter(item => item.completed).length;
    return Math.round((processedItems / missingInfo.length) * 100);
  };

  // Handle missing info input update (NO auto-progression)
  const updateMissingInfoInput = (id: string, value: string) => {
    setMissingInfo(prev =>
      prev.map(item =>
        item.id === id
          ? { ...item, userInput: value }  // DO NOT set completed here!
          : item
      )
    );
  };

  // Skip current missing info item
  const skipCurrentMissingInfo = () => {
    const currentItem = getCurrentMissingInfoItem();
    if (!currentItem) return;

    // Mark as completed but with empty input (skipped)
    setMissingInfo(prev => {
      const updatedItems = prev.map(item =>
        item.id === currentItem.id
          ? { ...item, userInput: '', completed: true, skipped: true }
          : item
      );

      // Calculate remaining incomplete items after this update
      const remainingIncomplete = updatedItems.filter(item => !item.completed);

      // Move to next step or finish
      if (remainingIncomplete.length > 0) {
        // Stay at same step index since we just completed one item
        // The next incomplete item will now be at the current index
      } else {
        // All items processed, go to final generation
        setTimeout(() => handleFinalProcessing(), 100);
      }

      return updatedItems;
    });
  };

  // Get current missing info item
  const getCurrentMissingInfoItem = () => {
    // Get all items that haven't been processed yet (not completed and not skipped)
    const unprocessedItems = missingInfo.filter(item => !item.completed);
    return unprocessedItems[currentMissingInfoStep] || null;
  };

  // Handle next missing info step (MANUAL ONLY)
  const handleNextMissingInfo = async () => {
    const currentItem = getCurrentMissingInfoItem();
    if (!currentItem) return;

    // Mark current item as completed when user explicitly clicks Next
    setMissingInfo(prev => {
      const updatedItems = prev.map(item =>
        item.id === currentItem.id
          ? { ...item, completed: true }
          : item
      );

      // Calculate remaining incomplete items after this update
      const remainingIncomplete = updatedItems.filter(item => !item.completed);

      // Move to next step or finish
      if (remainingIncomplete.length > 0) {
        // Stay at same step index since we just completed one item
        // The next incomplete item will now be at the current index
      } else {
        // All missing info completed, go to final processing
        setTimeout(() => handleFinalProcessing(), 100);
      }

      return updatedItems;
    });

    // Update live SOW preview with new information
    await updateLiveSOWPreview();
  };

  // Handle previous missing info step
  const handlePreviousMissingInfo = () => {
    if (currentMissingInfoStep > 0) {
      setCurrentMissingInfoStep(currentMissingInfoStep - 1);
    } else {
      // Go back to info form
      setStep('info');
    }
  };

  // Get all processed items (completed or skipped) for navigation
  const getProcessedItems = () => {
    return missingInfo.filter(item => item.completed);
  };

  // Update live SOW preview
  const updateLiveSOWPreview = async () => {
    if (!template) return;

    try {
      // Build enhanced prompt with collected info (excluding skipped items)
      const collectedInfo = missingInfo
        .filter(item => item.completed && !item.skipped && item.userInput?.trim())
        .map(item => `${item.title}: ${item.userInput}`)
        .join('\n');

      const enhancedPrompt = buildSmartPrompt() +
        (collectedInfo ? `\n\nAdditional Information:\n${collectedInfo}` : '');

      const response = await fetch('/api/gemini/preview', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          markdown: template.markdown,
          prompt: enhancedPrompt,
          templateId: template.id
        }),
      });

      if (response.ok) {
        const result = await response.json();
        setLiveSOWPreview(result.preview || '');
      }
    } catch (error) {
      console.error('Failed to update live preview:', error);
    }
  };

  const loadAvailableTemplates = async () => {
    try {
      console.log('📂 Loading available templates...');

      // Load both file-based saved templates and uploaded templates
      const [savedTemplatesResponse, uploadedTemplatesResponse] = await Promise.allSettled([
        fetch('/api/templates/save'), // File-based saved templates
        fetch('/api/template/list')   // Uploaded template files
      ]);

      const allTemplates = [];

      // Process saved templates (from SOW generator saves)
      if (savedTemplatesResponse.status === 'fulfilled' && savedTemplatesResponse.value.ok) {
        const savedTemplates = await savedTemplatesResponse.value.json();
        console.log('📋 Loaded saved templates:', savedTemplates.length);

        // Convert saved templates to the expected format
        const formattedSavedTemplates = savedTemplates.map((template: any) => ({
          id: template.id,
          name: template.name,
          uploadDate: template.createdAt,
          fileSize: template.markdown?.length || 0,
          fields: template.sections || [],
          type: 'saved',
          markdown: template.markdown,
          savedFormData: template.savedFormData
        }));

        allTemplates.push(...formattedSavedTemplates);
      }

      // Process uploaded templates (from file uploads)
      if (uploadedTemplatesResponse.status === 'fulfilled' && uploadedTemplatesResponse.value.ok) {
        const uploadedTemplates = await uploadedTemplatesResponse.value.json();
        console.log('📁 Loaded uploaded templates:', uploadedTemplates.length);

        // Add uploaded templates with type identifier
        const formattedUploadedTemplates = uploadedTemplates.map((template: any) => ({
          ...template,
          type: 'uploaded'
        }));

        allTemplates.push(...formattedUploadedTemplates);
      }

      console.log('✅ Total templates loaded:', allTemplates.length);
      console.log('📋 TEMPLATE LOADING: All templates details:', allTemplates);
      setAvailableTemplates(allTemplates);

    } catch (error) {
      console.error('❌ Failed to load templates:', error);
    }
  };

  // Load static variables
  const loadStaticVariables = useCallback(async () => {
    try {
      // Load comprehensive static variables
      const staticResponse = await fetch('/api/static-variables');
      if (staticResponse.ok) {
        const staticData = await staticResponse.json();
        setStaticVariables(staticData.staticVariables);

        // Update auto-suggestions with comprehensive data
        setAutoSuggestions(prev => ({
          ...prev,
          projectTypes: staticData.staticVariables.commonProjectTypes || prev.projectTypes,
          durations: staticData.staticVariables.timelineOptions || prev.durations,
          deliverables: staticData.staticVariables.commonDeliverables || prev.deliverables,

          milestones: staticData.projectDefaults.standardMilestones || prev.milestones,
          projectPhases: ['Discovery & Planning', 'Design Phase', 'Development Phase', 'Testing Phase', 'Deployment Phase', 'Support Phase']
        }));

        // Auto-populate form with comprehensive static variables
        setFormData(prev => ({
          ...prev,
          // Timeline defaults
          startDate: prev.startDate || staticData.staticVariables.defaultStartDate,
          endDate: prev.endDate || staticData.staticVariables.defaultEndDate,
          duration: prev.duration || staticData.projectDefaults.defaultTimeline,

          // Pricing defaults
          hourlyRate: prev.hourlyRate || staticData.projectDefaults.defaultHourlyRate,
          totalBudget: prev.totalBudget || staticData.projectDefaults.defaultBudgetRange,


          // Project defaults
          projectType: prev.projectType || staticData.projectDefaults.defaultProjectType,
          deliverables: (prev.deliverables && prev.deliverables.length > 0) ? prev.deliverables : staticData.staticVariables.commonDeliverables?.slice(0, 3) || [],
          milestones: (prev.milestones && prev.milestones.length > 0) ? prev.milestones : staticData.projectDefaults.standardMilestones || [],

          // Standard terms and processes
          changeRequestProcess: prev.changeRequestProcess || 'Change requests must be submitted in writing and will be subject to additional time and cost estimates.',
          communicationPlan: prev.communicationPlan || 'Weekly status meetings and email updates. Primary contact via email and scheduled calls.',
          qualityAssurance: prev.qualityAssurance || 'Comprehensive testing including unit tests, integration tests, and user acceptance testing.',
          supportAndMaintenance: prev.supportAndMaintenance || '30 days of post-launch support included. Extended support available under separate agreement.',
          intellectualProperty: prev.intellectualProperty || 'All custom work and intellectual property will transfer to client upon full payment.',
          confidentiality: prev.confidentiality || 'Both parties agree to maintain confidentiality of proprietary information.',
          terminationClause: prev.terminationClause || 'Either party may terminate with 30 days written notice. Payment due for work completed.'
        }));

        console.log('✅ STATIC VARIABLES: Comprehensive data loaded and applied');
      } else {
        // Fallback to basic auto-fill
        await autoFillDefaults();
      }
    } catch (error) {
      console.error('❌ STATIC VARIABLES: Failed to load comprehensive data:', error);
      // Still try to auto-fill with defaults
      await autoFillDefaults();
    }
  }, []);

  // Analyze template with Gemini AI
  const analyzeTemplateWithGemini = useCallback(async (templateId: string, markdown: string) => {
    setIsAnalyzing(true);
    try {
      console.log('🔍 GEMINI ANALYSIS: Starting intelligent template analysis...');

      const response = await fetch('/api/gemini/analyze-template', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          templateId,
          markdown
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.warn('❌ GEMINI ANALYSIS: Failed, using fallback:', errorData.error);

        // Use fallback analysis if provided
        if (errorData.fallbackAnalysis) {
          setTemplateAnalysis(errorData.fallbackAnalysis);
          return errorData.fallbackAnalysis;
        }
        throw new Error(errorData.error || 'Template analysis failed');
      }

      const result = await response.json();
      console.log('✅ GEMINI ANALYSIS: Template analysis complete:', result.analysis);

      setTemplateAnalysis(result.analysis);
      return result.analysis;
    } catch (error) {
      console.error('❌ GEMINI ANALYSIS: Analysis failed:', error);

      // Create basic fallback analysis
      const fallbackAnalysis = {
        requiredFields: [
          {
            id: 'client_name',
            title: 'Client Name',
            description: 'Primary contact person at the client company',
            category: 'Client Information',
            priority: 'high',
            examples: ['John Smith', 'Sarah Johnson'],
            inputType: 'text',
            validation: 'required'
          },
          {
            id: 'project_name',
            title: 'Project Name',
            description: 'Name or title of the project',
            category: 'Project Information',
            priority: 'high',
            examples: ['Website Redesign', 'Mobile App Development'],
            inputType: 'text',
            validation: 'required'
          }
        ],
        optionalFields: [],
        autoFillableFields: [],
        templateType: 'general_sow',
        complexity: 'moderate',
        recommendations: ['Fill out client information first', 'Define project scope clearly'],
        estimatedCompletionTime: '5-7 minutes'
      };

      setTemplateAnalysis(fallbackAnalysis);
      return fallbackAnalysis;
    } finally {
      setIsAnalyzing(false);
    }
  }, []);

  const loadTemplateById = useCallback(async (templateId: string, loadSavedData: boolean = false) => {
    setIsProcessing(true);
    setError(null);

    try {
      console.log('📤 TEMPLATE: Loading template from library...');

      // First, determine the template type from our available templates
      const templateInfo = availableTemplates.find(t => t.id === templateId);

      let response;
      if (templateInfo?.type === 'saved') {
        // Load from saved templates (file-based storage)
        console.log('📂 Loading saved template:', templateId);
        response = await fetch(`/api/templates/${templateId}`);
      } else {
        // Load from uploaded templates (uploads directory)
        console.log('📂 Loading uploaded template:', templateId);
        response = await fetch(`/api/template/preview?id=${templateId}`);
      }

      if (!response.ok) {
        throw new Error('Failed to load template');
      }

      const templateData = await response.json();

      // Handle different template structures
      let fields = [];
      if (templateInfo?.type === 'saved') {
        // For saved templates, extract fields from markdown since they're not pre-stored
        fields = scanTemplateFields(templateData.markdown).map(field => field.name);
      } else {
        // For uploaded templates, use pre-extracted fields
        fields = templateData.fields || [];
      }

      setTemplate({
        id: templateData.id,
        name: templateData.name,
        markdown: templateData.markdown,
        fields: fields
      });

      console.log('✅ TEMPLATE: Template loaded successfully, starting Gemini analysis...');

      // Analyze template with Gemini AI for intelligent field detection
      await analyzeTemplateWithGemini(templateData.id, templateData.markdown);

      // Also scan template for basic field suggestions (fallback)
      const detectedFieldSuggestions = scanTemplateFields(templateData.markdown);
      setDetectedFields(detectedFieldSuggestions);

      // Load static variables
      await loadStaticVariables();

      // Load saved user data if requested
      if (loadSavedData) {
        let savedData = null;

        if (templateInfo?.type === 'saved' && templateData.savedFormData) {
          // For saved templates, use the savedFormData from the template
          savedData = templateData.savedFormData;
          console.log('📋 Loading saved form data from saved template');
        } else if (UserDataService.hasUserData(templateId)) {
          // For uploaded templates, use UserDataService
          savedData = await UserDataService.getUserData(templateId);
          console.log('📋 Loading saved form data from UserDataService');
        }

        if (savedData) {
          try {
            // Map saved data structure to form data structure
            setFormData(prev => ({
              ...prev,
              // Client Information
              clientName: savedData.clientName || prev.clientName,
              clientEmail: savedData.clientEmail || prev.clientEmail,
              clientCompany: savedData.clientCompany || prev.clientCompany,
              clientPhone: savedData.clientPhone || prev.clientPhone,
              clientAddress: savedData.clientAddress || prev.clientAddress,
              clientTitle: savedData.clientTitle || prev.clientTitle,
              clientDepartment: savedData.clientDepartment || prev.clientDepartment,

              // Project Information
              projectName: savedData.projectName || prev.projectName,
              projectType: savedData.projectType || prev.projectType,
              projectDescription: savedData.projectDescription || prev.projectDescription,

              // Timeline
              startDate: savedData.startDate || prev.startDate,
              endDate: savedData.endDate || prev.endDate,
              duration: savedData.duration || prev.duration,

              // Pricing
              hourlyRate: savedData.hourlyRate || prev.hourlyRate,
              estimatedHours: savedData.estimatedHours || prev.estimatedHours,
              totalBudget: savedData.totalBudget || prev.totalBudget,

              // Payment Schedule
              paymentSchedule: savedData.paymentSchedule?.map(p => ({
                id: p.id,
                description: p.description,
                amount: p.amount,
                dueDate: p.dueDate,
                milestone: p.description
              })) || prev.paymentSchedule,

              // User/Company Information (map from saved data)
              userCompanyName: savedData.companyName || savedData.customFields?.userCompanyName || prev.userCompanyName,
              userContactName: savedData.contactName || savedData.customFields?.userContactName || prev.userContactName,
              userEmail: savedData.companyEmail || savedData.customFields?.userEmail || prev.userEmail,
              userPhone: savedData.companyPhone || savedData.customFields?.userPhone || prev.userPhone,
              userAddress: savedData.companyAddress || savedData.customFields?.userAddress || prev.userAddress,
              userTitle: savedData.contactTitle || savedData.customFields?.userTitle || prev.userTitle,
              userDepartment: savedData.customFields?.userDepartment || prev.userDepartment,

              // Custom fields from saved data
              deliverables: savedData.customFields?.deliverables || prev.deliverables,
              requirements: savedData.customFields?.requirements || prev.requirements,
              milestones: savedData.customFields?.milestones || prev.milestones,
              additionalRequirements: savedData.customFields?.additionalRequirements || prev.additionalRequirements,
              changeRequestProcess: savedData.customFields?.changeRequestProcess || prev.changeRequestProcess,
              communicationPlan: savedData.customFields?.communicationPlan || prev.communicationPlan,
              qualityAssurance: savedData.customFields?.qualityAssurance || prev.qualityAssurance,
              supportAndMaintenance: savedData.customFields?.supportAndMaintenance || prev.supportAndMaintenance,
              intellectualProperty: savedData.customFields?.intellectualProperty || prev.intellectualProperty,
              confidentiality: savedData.customFields?.confidentiality || prev.confidentiality,
              terminationClause: savedData.customFields?.terminationClause || prev.terminationClause
            }));

            showNotification({
              type: 'success',
              title: 'Data Loaded',
              message: 'Your saved information has been loaded successfully!'
            });
            console.log('✅ SAVED DATA: User data loaded and mapped successfully');
            console.log('📊 SAVED DATA MAPPING:', {
              originalSavedData: savedData,
              mappedFormData: {
                clientName: savedData.clientName,
                projectName: savedData.projectName,
                totalBudget: savedData.totalBudget,
                userCompanyName: savedData.companyName || savedData.customFields?.userCompanyName,
                userContactName: savedData.contactName || savedData.customFields?.userContactName
              }
            });
          } catch (error) {
            console.error('❌ SAVED DATA: Failed to load user data:', error);
            showNotification({
              type: 'warning',
              title: 'Data Load Warning',
              message: 'Could not load saved data, but template loaded successfully.'
            });
          }
        }
      } else if (!loadSavedData) {
        // Clear any existing saved data from form if user chose not to load it
        showNotification({
          type: 'info',
          title: 'Fresh Start',
          message: 'Template loaded without saved data. Starting fresh!'
        });
      }

      setStep('info');
      console.log('🎉 COMPLETE: Template selection and analysis complete!');
    } catch (error) {
      console.error('❌ TEMPLATE: Failed to load template:', error);
      setError(error instanceof Error ? error.message : 'Failed to load template');
    } finally {
      setIsProcessing(false);
    }
  }, [analyzeTemplateWithGemini, loadStaticVariables, showNotification]);

  // Scan template for fields and placeholders
  const scanTemplateFields = (markdown: string): string[] => {
    const fields = new Set<string>();

    // Common field patterns to look for
    const patterns = [
      /\[([A-Z_]+)\]/g,           // [CLIENT_NAME], [PROJECT_NAME]
      /\{([A-Z_]+)\}/g,           // {CLIENT_NAME}, {PROJECT_NAME}
      /\$\{([A-Z_]+)\}/g,         // ${CLIENT_NAME}, ${PROJECT_NAME}
      /\{\{([A-Z_]+)\}\}/g,       // {{CLIENT_NAME}}, {{PROJECT_NAME}}
      /\[([a-z_]+)\]/g,           // [client_name], [project_name]
      /\{([a-z_]+)\}/g,           // {client_name}, {project_name}
    ];

    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(markdown)) !== null) {
        fields.add(match[1]);
      }
    });

    // Convert field names to human-readable suggestions
    const fieldSuggestions = Array.from(fields).map(field => {
      const normalized = field.toLowerCase().replace(/_/g, ' ');
      switch (normalized) {
        case 'client name':
        case 'client_name':
          return 'Client contact name (e.g., John Smith)';
        case 'client company':
        case 'client_company':
          return 'Client company name (e.g., Acme Corporation)';
        case 'project name':
        case 'project_name':
          return 'Project title (e.g., Website Redesign)';
        case 'start date':
        case 'start_date':
          return 'Project start date (e.g., January 15, 2024)';
        case 'end date':
        case 'end_date':
          return 'Project end date (e.g., March 30, 2024)';
        case 'budget':
        case 'total cost':
        case 'total_cost':
          return 'Total project budget (e.g., $15,000)';
        case 'hourly rate':
        case 'hourly_rate':
          return 'Hourly rate (e.g., $150/hour)';
        case 'timeline':
          return 'Project timeline (e.g., 8 weeks)';
        case 'scope':
          return 'Project scope and deliverables';
        case 'description':
          return 'Project description';
        default:
          return `${normalized.charAt(0).toUpperCase() + normalized.slice(1)} information`;
      }
    });

    return fieldSuggestions;
  };

  // Load available templates and initialize defaults
  useEffect(() => {
    loadAvailableTemplates();
    autoFillDefaults(); // Initialize with smart defaults

    // Check if templateId is provided in URL
    const urlParams = new URLSearchParams(window.location.search);
    const templateId = urlParams.get('templateId');
    const editMode = urlParams.get('edit') === 'true';

    if (templateId) {
      // Load with saved data if in edit mode, otherwise load fresh
      loadTemplateById(templateId, editMode);

      // If in edit mode, show a notification
      if (editMode) {
        console.log('🔧 Edit mode activated - loading saved data for editing');
      }
    }
  }, [loadTemplateById]);

  // Handle file upload with intelligent analysis
  const handleFileUpload = async (file: File) => {
    if (!file.name.toLowerCase().endsWith('.docx')) {
      setError('Please upload a DOCX file only.');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      console.log('📤 UPLOAD: Starting file upload and analysis...');

      const formData = new FormData();
      formData.append('template', file);
      formData.append('saveAsTemplate', saveAsTemplate.toString());

      const response = await fetch('/api/template/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const result = await response.json();

      setTemplate({
        id: result.id,
        name: result.name,
        markdown: result.markdown,
        fields: result.fields
      });

      console.log('✅ UPLOAD: File uploaded successfully, starting Gemini analysis...');

      // Analyze template with Gemini AI for intelligent field detection
      await analyzeTemplateWithGemini(result.id, result.markdown);

      // Also scan template for basic field suggestions (fallback)
      const detectedFieldSuggestions = scanTemplateFields(result.markdown);
      setDetectedFields(detectedFieldSuggestions);

      // Load static variables
      await loadStaticVariables();

      setStep('info');
      console.log('🎉 COMPLETE: Template upload and analysis complete!');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Upload failed');
    } finally {
      setIsProcessing(false);
    }
  };

  // NEW WORKFLOW: Handle client info submission - goes directly to Gemini processing
  const handleClientInfoSubmit = async () => {
    if (!template) return;

    setIsProcessing(true);
    setError(null);
    setStep('processing');

    try {
      console.log('🚀 NEW WORKFLOW: Starting initial Gemini processing...');

      // Step 1: Send to Gemini for initial processing
      const processResponse = await fetch('/api/gemini/process', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          markdown: template.markdown,
          prompt: buildSmartPrompt(),
          templateId: template.id,
          isReviewStep: false
        }),
      });

      if (!processResponse.ok) {
        const errorData = await processResponse.json().catch(() => ({ error: 'Unknown error' }));
        throw new Error(errorData.error || `Initial processing failed with status ${processResponse.status}`);
      }

      const processData = await processResponse.json();
      console.log('✅ Initial processing complete');

      // Store the initially processed markdown
      setInitialProcessedMarkdown(processData.updatedMarkdown);
      setProcessedMarkdown(processData.updatedMarkdown);

      // Step 2: Convert to DOCX for visual review
      await convertToDocxForReview(processData.updatedMarkdown);

    } catch (err) {
      console.error('❌ Initial processing error:', err);
      setError(err instanceof Error ? err.message : 'Failed to process information');
      setIsProcessing(false);
    }
  };

  // NEW WORKFLOW: Convert processed markdown to DOCX for visual review
  const convertToDocxForReview = async (processedMarkdown: string) => {
    try {
      console.log('📄 Converting to DOCX for visual review...');
      setIsConvertingDocx(true);

      // Use the new preview API that returns DOCX buffer for preview
      const docxResponse = await fetch('/api/docx/preview', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          markdown: processedMarkdown,
          templateId: template?.id,
          clientName: formData.clientName || 'Client'
        }),
      });

      if (!docxResponse.ok) {
        throw new Error(`DOCX preview generation failed with status ${docxResponse.status}`);
      }

      const docxData = await docxResponse.json();
      console.log('✅ DOCX preview generation complete, size:', docxData.size);

      // Convert array back to ArrayBuffer for consistent handling
      const docxBuffer = new Uint8Array(docxData.docxBuffer).buffer;
      setCurrentDocxBuffer(docxBuffer);

      // Step 3: Analyze for missing information using Gemini
      await analyzeForMissingInfo(processedMarkdown);

    } catch (error) {
      console.error('❌ DOCX conversion error:', error);
      setError('Failed to convert document to DOCX: ' + (error instanceof Error ? error.message : 'Unknown error'));
      setIsProcessing(false);
    } finally {
      setIsConvertingDocx(false);
    }
  };

  // NEW WORKFLOW: Analyze processed document for missing information
  const analyzeForMissingInfo = async (processedMarkdown: string) => {
    try {
      console.log('🔍 Analyzing for missing information...');

      const missingInfoResponse = await fetch('/api/gemini/missing-info', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          processedMarkdown: processedMarkdown,
          originalTemplate: template?.markdown,
          userPrompt: buildSmartPrompt(),
          templateId: template?.id
        }),
      });

      if (!missingInfoResponse.ok) {
        throw new Error(`Missing info analysis failed with status ${missingInfoResponse.status}`);
      }

      const analysisData = await missingInfoResponse.json();
      console.log('✅ Missing info analysis complete');

      // Set analysis results
      setDocumentSummary(analysisData.documentSummary || '');
      setCompletionScore(analysisData.completionScore || 85);

      // Transform missing info for the UI
      const structuredMissingInfo = (analysisData.missingInfo || []).map((item: any, index: number) => ({
        id: item.id || `item_${index}`,
        title: item.title || 'Missing Information',
        description: item.description || 'Additional information needed',
        priority: item.priority || 'medium',
        category: item.category || 'General',
        examples: item.examples || '',
        currentState: item.currentState || '',
        needed: item.needed || '',
        section: item.section || '',
        completed: false,
        userInput: '',
        skipped: false
      }));

      setMissingInfo(structuredMissingInfo);

      // Move to visual review step
      setStep('docx-review');

    } catch (error) {
      console.error('❌ Missing info analysis error:', error);
      setError('Failed to analyze document: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setIsProcessing(false);
    }
  };

  // NEW WORKFLOW: Handle user updates from visual review
  const handleUserUpdates = async () => {
    if (!template || !initialProcessedMarkdown) return;

    setIsProcessing(true);
    setError(null);
    setStep('final-processing');

    try {
      console.log('🔄 Processing user updates...');

      // Build enhanced prompt with user updates
      const collectedInfo = missingInfo
        .filter(item => item.completed && !item.skipped && item.userInput?.trim())
        .map(item => `${item.title}: ${item.userInput}`)
        .join('\n');

      // Check if user actually added any new information
      const hasNewInfo = collectedInfo.trim().length > 0;

      if (hasNewInfo) {
        // User added new information - process it
        console.log('📝 User provided additional information, processing updates...');

        const enhancedPrompt = buildSmartPrompt() +
          `\n\nAdditional Information:\n${collectedInfo}`;

        // Send back to Gemini for final processing
        const finalProcessResponse = await fetch('/api/gemini/process', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            markdown: initialProcessedMarkdown, // Use the initial processed version as base
            prompt: enhancedPrompt,
            templateId: template.id,
            isReviewStep: false,
            skippedFields: missingInfo.filter(item => item.skipped).map(item => item.title)
          }),
        });

        if (!finalProcessResponse.ok) {
          const errorData = await finalProcessResponse.json().catch(() => ({ error: 'Unknown error' }));
          throw new Error(errorData.error || `Final processing failed with status ${finalProcessResponse.status}`);
        }

        const finalData = await finalProcessResponse.json();
        console.log('✅ Final processing with updates complete');

        setProcessedMarkdown(finalData.updatedMarkdown);

        // Convert updated version to DOCX
        await convertFinalDocx(finalData.updatedMarkdown);
      } else {
        // No new information - use the exact document from review stage
        console.log('✅ No additional information provided - using exact review document as final');

        // Use the current processed markdown directly (what user saw in review)
        setProcessedMarkdown(initialProcessedMarkdown);

        // Convert the review document directly to final DOCX (no additional processing)
        await convertFinalDocx(initialProcessedMarkdown);
      }

    } catch (error) {
      console.error('❌ User updates processing error:', error);
      setError('Failed to process updates: ' + (error instanceof Error ? error.message : 'Unknown error'));
      setStep('docx-review');
    } finally {
      setIsProcessing(false);
    }
  };

  // NEW WORKFLOW: Convert final document to DOCX (no additional processing)
  const convertFinalDocx = async (finalMarkdown: string) => {
    try {
      console.log('📄 Converting final document to DOCX with perfect format preservation...');

      // Use the template conversion API that preserves formatting perfectly
      // This maintains the exact formatting from the original template
      const docxResponse = await fetch('/api/template/convert', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          templateId: template?.id,
          markdown: finalMarkdown
        }),
      });

      if (!docxResponse.ok) {
        throw new Error(`Final DOCX conversion failed with status ${docxResponse.status}`);
      }

      const docxBuffer = await docxResponse.arrayBuffer();
      console.log('✅ Final DOCX ready - exact document from review stage with perfect formatting');

      setGeneratedDocxBuffer(docxBuffer);
      setStep('preview');

    } catch (error) {
      console.error('❌ Final DOCX conversion error:', error);
      setError('Failed to generate final document: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  };

  // Handle final processing after review (LEGACY - keeping for compatibility)
  const handleFinalProcessing = async () => {
    if (!template) return;

    setIsProcessing(true);
    setError(null);
    setStep('processing');

    try {
      console.log('Submitting to Gemini API for final processing...');

      // Build enhanced prompt with collected info (excluding skipped items)
      const collectedInfo = missingInfo
        .filter(item => item.completed && !item.skipped && item.userInput?.trim())
        .map(item => `${item.title}: ${item.userInput}`)
        .join('\n');

      const enhancedPrompt = buildSmartPrompt() +
        (collectedInfo ? `\n\nAdditional Information:\n${collectedInfo}` : '') +
        (additionalInfo ? `\n\nExtra Details:\n${additionalInfo}` : '');

      // Send to Gemini API for final processing
      const response = await fetch('/api/gemini/process', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          markdown: template.markdown,
          prompt: enhancedPrompt,
          templateId: template.id,
          isReviewStep: false, // Final processing
          skippedFields: missingInfo.filter(item => item.skipped).map(item => item.title)
        }),
      });

      console.log('Gemini API response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        console.error('Gemini API error response:', errorData);
        throw new Error(errorData.error || `Processing failed with status ${response.status}`);
      }

      const result = await response.json();
      console.log('Gemini API success, result:', result);

      setProcessedMarkdown(result.updatedMarkdown);

      // Generate DOCX file with the updated content for preview
      console.log('🔄 FETCH STEP 1: Starting DOCX generation for preview...');
      console.log('Template ID:', template.id);
      console.log('Markdown length:', result.updatedMarkdown.length);

      const docxResponse = await fetch('/api/template/convert', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        },
        body: JSON.stringify({
          templateId: template.id,
          markdown: result.updatedMarkdown
        }),
      });

      console.log('🔄 FETCH STEP 2: Response received');
      console.log('Response status:', docxResponse.status);
      console.log('Response headers:', Object.fromEntries(docxResponse.headers.entries()));

      if (docxResponse.ok) {
        console.log('🔄 FETCH STEP 3: Converting response to ArrayBuffer...');
        const docxArrayBuffer = await docxResponse.arrayBuffer();

        console.log('🔄 FETCH STEP 4: ArrayBuffer received');
        console.log('Buffer size:', docxArrayBuffer.byteLength);
        console.log('Buffer type:', docxArrayBuffer.constructor.name);
        console.log('Buffer first 20 bytes:', Array.from(new Uint8Array(docxArrayBuffer.slice(0, 20))));

        // Verify it's a valid ZIP/DOCX file (should start with PK)
        const firstBytes = new Uint8Array(docxArrayBuffer.slice(0, 2));
        const zipSignature = Array.from(firstBytes).map(b => b.toString(16).padStart(2, '0')).join('');
        console.log('🔄 FETCH STEP 5: ZIP signature validation');
        console.log('ZIP signature:', zipSignature);

        if (firstBytes[0] === 0x50 && firstBytes[1] === 0x4B) {
          console.log('✅ Generated DOCX has valid ZIP signature');
          console.log('🔄 FETCH STEP 6: Setting buffer state...');
          setGeneratedDocxBuffer(docxArrayBuffer);
          console.log('✅ Buffer state set successfully');
        } else {
          console.error('❌ Generated DOCX has invalid ZIP signature:', zipSignature);
          setGeneratedDocxBuffer(null);
        }
      } else {
        console.error('❌ DOCX generation failed');
        console.error('Response status:', docxResponse.status);
        console.error('Response text:', await docxResponse.text());
        setGeneratedDocxBuffer(null);
      }

      setStep('preview');
    } catch (err) {
      console.error('Client-side error:', err);

      if (err instanceof TypeError && err.message.includes('fetch')) {
        setError('Network error: Unable to connect to AI service. Please check your connection and try again.');
      } else {
        setError(err instanceof Error ? err.message : 'Processing failed');
      }

      setStep('info');
    } finally {
      setIsProcessing(false);
    }
  };

  // Auto-save SOW to dashboard
  const autoSaveSOW = async (customTitle?: string) => {
    if (!template || !processedMarkdown) return;

    try {
      // Extract client and project info from the processed markdown with better patterns
      const clientCompanyMatch = processedMarkdown.match(/\*?\*?Client\s*Company:?\*?\*?\s*(.+)/i);
      const clientMatch = processedMarkdown.match(/\*?\*?Client:?\*?\*?\s*(.+)/i);
      const projectNameMatch = processedMarkdown.match(/\*?\*?Project\s*Name?:?\*?\*?\s*(.+)/i);
      const contactMatch = processedMarkdown.match(/\*?\*?Contact\s*(?:Person|Name)?:?\*?\*?\s*(.+)/i);

      const clientName = clientCompanyMatch ? clientCompanyMatch[1].trim() :
                        clientMatch ? clientMatch[1].trim() : 'Unknown Client';
      const projectName = projectNameMatch ? projectNameMatch[1].trim() : 'Untitled Project';
      const contactName = contactMatch ? contactMatch[1].trim() : clientName;

      // Use custom title if provided, otherwise generate default
      const title = customTitle || customSOWName || `${clientName} - ${projectName}`;

      await fetch('/api/sow/save', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title,
          clientName: contactName,
          projectName,
          templateName: template.name,
          templateId: template.id,
          markdown: processedMarkdown,
          status: 'final'
        }),
      });

      console.log('SOW auto-saved to dashboard with title:', title);
    } catch (error) {
      console.error('Failed to auto-save SOW:', error);
    }
  };

  // Handle download
  const testDocxPreviewLibrary = async () => {
    try {
      console.log('🧪 LIBRARY TEST: Testing docx-preview library...');

      // Test if the library can be imported
      const docxPreview = await import('docx-preview');
      console.log('✅ LIBRARY TEST: docx-preview imported successfully');
      console.log('Available methods:', Object.keys(docxPreview));
      console.log('renderAsync function:', typeof docxPreview.renderAsync);

      alert('Library imported successfully! Check console for details.');

    } catch (error) {
      console.error('❌ LIBRARY TEST: Failed:', error);
      alert('Library test failed: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  };

  const testOriginalTemplate = async () => {
    if (!template) return;

    try {
      console.log('🧪 TESTING: Starting original template test...');
      const response = await fetch(`/api/template/${template.id}/test-preview`);

      console.log('🧪 TESTING: Response status:', response.status);
      console.log('🧪 TESTING: Response headers:', Object.fromEntries(response.headers.entries()));

      if (response.ok) {
        const originalBuffer = await response.arrayBuffer();
        console.log('🧪 TESTING: Original template buffer received');
        console.log('🧪 TESTING: Buffer size:', originalBuffer.byteLength);
        console.log('🧪 TESTING: Buffer first 20 bytes:', Array.from(new Uint8Array(originalBuffer.slice(0, 20))));

        // Verify ZIP signature
        const firstBytes = new Uint8Array(originalBuffer.slice(0, 2));
        if (firstBytes[0] === 0x50 && firstBytes[1] === 0x4B) {
          console.log('✅ TESTING: Original template has valid ZIP signature');
          console.log('🧪 TESTING: Setting buffer for preview...');
          setGeneratedDocxBuffer(originalBuffer);
          console.log('✅ TESTING: Buffer set - should trigger preview');
        } else {
          console.error('❌ TESTING: Original template has invalid ZIP signature');
        }
      } else {
        console.error('❌ TESTING: Failed to load original template');
        console.error('Response text:', await response.text());
      }
    } catch (error) {
      console.error('❌ TESTING: Error testing original template:', error);
    }
  };

  const handleDownload = async () => {
    if (!template || !processedMarkdown) {
      setError('No template or processed content available for download');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      console.log('Starting download with template ID:', template.id);
      console.log('Processed markdown length:', processedMarkdown.length);

      const response = await fetch('/api/template/convert', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        },
        body: JSON.stringify({
          templateId: template.id,
          markdown: processedMarkdown
        }),
      });

      console.log('Convert API response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        throw new Error(errorData.error || `Download failed with status ${response.status}`);
      }

      const blob = await response.blob();
      console.log('Downloaded blob size:', blob.size);

      if (blob.size === 0) {
        throw new Error('Downloaded file is empty');
      }

      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `SOW-Generated-${new Date().toISOString().split('T')[0]}.docx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      // Auto-save to dashboard after successful download
      await autoSaveSOW();

    } catch (err) {
      console.error('Download error:', err);
      setError(err instanceof Error ? err.message : 'Download failed');
    } finally {
      setIsProcessing(false);
    }
  };

  const renderUploadStep = () => (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-6">
        <h2 className="text-3xl font-bold text-white mb-4">Choose Your Template</h2>
        <p className="text-slate-300">Upload a new DOCX template or select from your saved templates</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Upload New Template */}
        <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8">
          <h3 className="text-xl font-bold text-white mb-4 text-center">Upload New Template</h3>
          <div
            className="border-2 border-dashed border-white/30 rounded-2xl p-8 text-center hover:border-white/50 transition-all duration-300"
            onDrop={(e) => {
              e.preventDefault();
              const file = e.dataTransfer.files[0];
              if (file) handleFileUpload(file);
            }}
            onDragOver={(e) => e.preventDefault()}
          >
            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h4 className="text-lg font-bold text-white mb-2">Drop DOCX file here</h4>
            <p className="text-slate-400 mb-4 text-sm">or click to browse</p>
            <input
              type="file"
              accept=".docx"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) handleFileUpload(file);
              }}
              className="hidden"
              id="file-upload"
            />
            <label
              htmlFor="file-upload"
              className="px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 font-bold transition-all duration-300 cursor-pointer inline-block text-sm"
            >
              Choose File
            </label>
          </div>

          {/* Save as Template Option */}
          <div className="mt-6 p-4 bg-white/5 border border-white/10 rounded-xl">
            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                id="save-as-template"
                checked={saveAsTemplate}
                onChange={(e) => setSaveAsTemplate(e.target.checked)}
                className="w-5 h-5 bg-white/10 border border-white/30 rounded text-blue-600 focus:ring-blue-500 focus:ring-2"
              />
              <label htmlFor="save-as-template" className="text-white font-medium cursor-pointer">
                <svg className="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                Save as template for future use
              </label>
            </div>
            <p className="text-slate-400 text-sm mt-2 ml-8">
              {saveAsTemplate
                ? "This document will be saved to your template library"
                : "This document will only be used once and not saved"
              }
            </p>
          </div>
        </div>

        {/* Select Existing Template */}
        <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8">
          <h3 className="text-xl font-bold text-white mb-4 text-center">Select Saved Template</h3>
          <div className="text-center">
            <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h4 className="text-lg font-bold text-white mb-2">Use Existing Template</h4>
            <p className="text-slate-400 mb-4 text-sm">Choose from your uploaded templates</p>
            <button
              onClick={() => {
                console.log('🔍 TEMPLATE SELECTOR: Opening template selector...');
                console.log('📊 TEMPLATE SELECTOR: Available templates count:', availableTemplates.length);
                console.log('📋 TEMPLATE SELECTOR: Available templates:', availableTemplates);
                setShowTemplateSelector(true);
              }}
              className="px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl hover:from-green-700 hover:to-emerald-700 font-bold transition-all duration-300 text-sm"
            >
              Browse Templates ({availableTemplates.length})
            </button>
          </div>
        </div>
      </div>

      {/* Enhanced Template Selector Modal with Full Management */}
      {showTemplateSelector && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-6 w-full max-w-7xl max-h-[90vh] overflow-hidden flex flex-col">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-4">
                <h2 className="text-2xl font-bold text-white">Template Manager</h2>
                {selectedTemplates.size > 0 && (
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-slate-300">
                      {selectedTemplates.size} selected
                    </span>
                    <button
                      onClick={handleBulkDelete}
                      className="px-3 py-1 bg-red-500/20 border border-red-400/30 text-red-300 rounded-lg hover:bg-red-500/30 transition-all duration-300 text-sm"
                    >
                      Delete Selected
                    </button>
                    <button
                      onClick={() => setSelectedTemplates(new Set())}
                      className="px-3 py-1 bg-gray-500/20 border border-gray-400/30 text-gray-300 rounded-lg hover:bg-gray-500/30 transition-all duration-300 text-sm"
                    >
                      Clear Selection
                    </button>
                  </div>
                )}
              </div>
              <div className="flex items-center space-x-2">
                {availableTemplates.length > 0 && (
                  <button
                    onClick={() => setIsSelectionMode(!isSelectionMode)}
                    className={`px-4 py-2 rounded-xl font-bold transition-all duration-300 text-sm ${
                      isSelectionMode
                        ? 'bg-blue-600 text-white'
                        : 'bg-white/10 border border-white/20 text-white hover:bg-white/20'
                    }`}
                  >
                    {isSelectionMode ? 'Exit Selection' : 'Select Multiple'}
                  </button>
                )}
                <button
                  onClick={() => setShowTemplateSelector(false)}
                  className="w-10 h-10 bg-red-500/20 border border-red-400/30 text-red-300 rounded-xl hover:bg-red-500/30 transition-all duration-300 flex items-center justify-center"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            <div className="flex-1 overflow-y-auto min-h-0">
              {(() => {
                console.log('🎨 TEMPLATE SELECTOR RENDER: Available templates:', availableTemplates.length);
                console.log('📋 TEMPLATE SELECTOR RENDER: Templates data:', availableTemplates);
                return availableTemplates.length > 0;
              })() ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pb-4">
                  {availableTemplates.map((template) => {
                    const hasUserData = UserDataService.hasUserData(template.id);
                    const isSelected = selectedTemplates.has(template.id);
                    return (
                      <div key={template.id} className={`group relative bg-gradient-to-br from-white/10 via-white/5 to-transparent backdrop-blur-xl border border-white/20 p-6 rounded-2xl hover:from-white/15 hover:via-white/10 hover:to-white/5 transition-all duration-500 transform hover:scale-[1.02] hover:shadow-2xl hover:shadow-purple-500/10 ${
                        isSelectionMode && isSelected ? 'ring-2 ring-blue-400 bg-blue-500/10' : ''
                      }`}>
                        {/* Selection checkbox */}
                        {isSelectionMode && (
                          <div className="absolute top-4 left-4">
                            <input
                              type="checkbox"
                              checked={isSelected}
                              onChange={() => toggleTemplateSelection(template.id)}
                              className="w-5 h-5 bg-white/10 border border-white/30 rounded text-blue-600 focus:ring-blue-500 focus:ring-2"
                            />
                          </div>
                        )}

                        <h3 className="text-white font-bold mb-2 break-words overflow-hidden">{template.name}</h3>
                        <p className="text-slate-300 text-sm mb-3">
                          {template.type === 'saved' ? 'Saved' : 'Uploaded'}: {new Date(template.uploadDate).toLocaleDateString()}
                          <br />
                          Size: {formatFileSize(template.fileSize)}
                          <br />
                          Fields: {template.fields?.length || 0} detected
                          {template.type === 'saved' && template.savedFormData && (
                            <>
                              <br />
                              <span className="text-green-400">✓ Has saved form data</span>
                            </>
                          )}
                        </p>

                        {/* Saved Data Indicator */}
                        {hasUserData && (
                          <div className="mb-3 p-2 bg-green-500/10 border border-green-400/30 rounded-lg">
                            <div className="flex items-center space-x-2">
                              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                              <span className="text-green-300 text-xs font-medium">
                                {UserDataService.getUserDataSummary(template.id)}
                              </span>
                            </div>
                          </div>
                        )}

                        {!isSelectionMode && (
                          <div className="space-y-2">
                            {/* Template Loading Options */}
                            <div className="space-y-2">
                              {(hasUserData || (template.type === 'saved' && template.savedFormData)) ? (
                                <>
                                  <button
                                    onClick={() => {
                                      if (template.type === 'saved') {
                                        loadTemplateWithFormData(template.id);
                                      } else {
                                        loadTemplateById(template.id, true);
                                      }
                                      setShowTemplateSelector(false);
                                    }}
                                    className="w-full py-2 px-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl hover:from-green-700 hover:to-emerald-700 font-bold transition-all duration-300 text-sm flex items-center justify-center"
                                  >
                                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    Load with Saved Data
                                  </button>
                                  <button
                                    onClick={() => {
                                      if (template.type === 'saved') {
                                        // For saved templates, load just the template without form data
                                        loadTemplateById(template.id, false);
                                      } else {
                                        loadTemplateById(template.id, false);
                                      }
                                      setShowTemplateSelector(false);
                                    }}
                                    className="w-full py-2 px-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 font-bold transition-all duration-300 text-sm flex items-center justify-center"
                                  >
                                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    Load Template Only
                                  </button>
                                </>
                              ) : (
                                <button
                                  onClick={() => {
                                    if (template.type === 'saved') {
                                      loadTemplateWithFormData(template.id);
                                    } else {
                                      loadTemplateById(template.id, false);
                                    }
                                    setShowTemplateSelector(false);
                                  }}
                                  className="w-full py-2 px-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 font-bold transition-all duration-300 text-sm flex items-center justify-center"
                                >
                                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                  </svg>
                                  Use This Template
                                </button>
                              )}
                            </div>

                            {/* Management Actions */}
                            <div className="flex gap-2">
                              <button
                                onClick={() => handlePreviewTemplate(template.id)}
                                className="flex-1 py-2 px-3 bg-green-500/20 border border-green-400/30 text-green-300 rounded-xl hover:bg-green-500/30 transition-all duration-300 text-sm flex items-center justify-center"
                                title="Preview DOCX"
                              >
                                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                </svg>
                                Preview
                              </button>
                              <button
                                onClick={() => handleDeleteTemplate(template.id)}
                                className="py-2 px-3 bg-red-500/20 border border-red-400/30 text-red-300 rounded-xl hover:bg-red-500/30 transition-all duration-300 text-sm flex items-center justify-center"
                                title="Delete Template"
                              >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-gradient-to-r from-gray-500 to-slate-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <h3 className="text-white font-bold mb-2">No Templates Found</h3>
                  <p className="text-slate-300 mb-4">Upload your first template to get started</p>
                  <button
                    onClick={() => {
                      setShowTemplateSelector(false);
                      // Trigger file input click
                      const fileInput = document.createElement('input');
                      fileInput.type = 'file';
                      fileInput.accept = '.docx';
                      fileInput.onchange = (e) => {
                        const file = (e.target as HTMLInputElement).files?.[0];
                        if (file) {
                          handleFileUpload(file);
                        }
                      };
                      fileInput.click();
                    }}
                    className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 font-bold transition-all duration-300"
                  >
                    Upload New Template
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* DOCX Preview Modal */}
      {previewTemplateId && (
        <DocxPreviewModal
          templateId={previewTemplateId}
          onClose={() => setPreviewTemplateId(null)}
        />
      )}
    </div>
  );

  // Enhanced auto-detection system with real-time analysis
  const detectFieldCompletion = useCallback((fieldValue: string, requirement: any) => {
    if (!fieldValue || !requirement) return false;

    const value = fieldValue.toLowerCase().trim();
    const title = requirement.title.toLowerCase();
    const description = requirement.description?.toLowerCase() || '';

    // Enhanced keyword matching with context awareness
    const keywords = value.split(/\s+/);
    const titleWords = title.split(/\s+/);
    const descWords = description.split(/\s+/);

    // Check for direct matches or semantic similarity
    const hasDirectMatch = titleWords.some((word: string) => keywords.includes(word)) ||
                          descWords.some((word: string) => keywords.includes(word));

    return hasDirectMatch || value.length > 10; // Basic completion check
  }, []);

  // Real-time field analysis with intelligent categorization
  const analyzeFormCompletion = useCallback(() => {
    if (!templateAnalysis) return { completed: [], missing: [] };

    const allRequirements = [
      ...(templateAnalysis.requiredFields || []),
      ...(templateAnalysis.optionalFields || []),
      ...(templateAnalysis.autoFillableFields || [])
    ];

    // Debug: Log what fields are being analyzed
    console.log('🔍 SMART ANALYSIS: All requirements being analyzed:', {
      requiredFields: templateAnalysis.requiredFields?.length || 0,
      optionalFields: templateAnalysis.optionalFields?.length || 0,
      autoFillableFields: templateAnalysis.autoFillableFields?.length || 0,
      totalRequirements: allRequirements.length
    });

    const completed: any[] = [];
    const missing: any[] = [];

    // Analyze each requirement against current form data
    allRequirements.forEach(req => {
      const isCompleted = detectFieldCompletion(formData.projectDescription, req);

      if (isCompleted) {
        completed.push(req);
      } else {
        missing.push(req);
      }
    });

    return { completed, missing };
  }, [templateAnalysis, formData, detectFieldCompletion]);



  // Auto-apply static variables when form loads
  React.useEffect(() => {
    if (staticVariables && !formData.startDate) {
      setFormData(prev => ({
        ...prev,
        startDate: staticVariables.defaultStartDate,
        endDate: staticVariables.defaultEndDate,
        hourlyRate: prev.hourlyRate || staticVariables.defaultHourlyRate || '150'
      }));
    }
  }, [staticVariables, formData.startDate]);

  // Auto-calculate budget when rate or hours change
  React.useEffect(() => {
    if (formData.hourlyRate && formData.estimatedHours) {
      calculateBudget();
    }
  }, [formData.hourlyRate, formData.estimatedHours, calculateBudget]);

  // Real-time auto-detection trigger
  React.useEffect(() => {
    // Trigger re-analysis when form data changes
    if (templateAnalysis) {
      const { completed, missing } = analyzeFormCompletion();
      // This will cause the Smart Analysis bar to update in real-time
    }
  }, [formData, templateAnalysis, analyzeFormCompletion]);

  const renderInfoStep = () => {
    const { completed, missing } = analyzeFormCompletion();

    return (
      <div className="max-w-7xl mx-auto">
        {/* Autosave Status - Only show when saving or there's an issue */}
        {template?.id && (isSaving || (hasUnsavedChanges && lastSaved && (Date.now() - lastSaved.getTime()) > 10000)) && (
          <div className="mb-4 flex items-center justify-center space-x-4">
              <AutosaveIndicator
                isSaving={isSaving}
                lastSaved={lastSaved}
                hasUnsavedChanges={hasUnsavedChanges}
              />

            </div>
          )}

        {/* Saved Data Management - Show separately when there's saved data */}
        {template?.id && UserDataService.hasUserData(template.id) && (
          <div className="mb-4 flex items-center justify-center space-x-4">
            <div className="flex items-center space-x-2 text-sm text-slate-400">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
              <span>{UserDataService.getUserDataSummary(template.id)}</span>
            </div>

            <button
              onClick={async () => {
                if (confirm('Are you sure you want to clear all saved data for this template?')) {
                  await UserDataService.deleteUserData(template.id);
                  // Reset form to empty state
                  setFormData({
                    clientName: '', clientEmail: '', clientCompany: '', clientPhone: '', clientAddress: '', clientTitle: '', clientDepartment: '',
                    projectName: '', projectType: '', startDate: '', endDate: '', duration: '4-6 weeks',
                    hourlyRate: '', estimatedHours: '', totalBudget: '', paymentSchedule: [], depositAmount: '', depositDueDate: '', finalPaymentAmount: '', finalPaymentDueDate: '',
                    projectDescription: '', deliverables: [], requirements: [], milestones: [], additionalRequirements: '',
                    changeRequestProcess: '', communicationPlan: '', qualityAssurance: '', supportAndMaintenance: '', intellectualProperty: '', confidentiality: '', terminationClause: '',
                    userCompanyName: '', userContactName: '', userEmail: '', userPhone: '', userAddress: '', userTitle: '', userDepartment: ''
                  });
                  alert('Saved data cleared successfully!');
                }
              }}
              className="text-xs px-2 py-1 bg-red-500/20 border border-red-400/30 text-red-300 rounded-lg hover:bg-red-500/30 transition-all duration-300"
              title="Clear saved data"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          </div>
        )}

        {/* Two Column Layout: Project Information + Smart Analysis */}
        <div className="flex flex-col lg:flex-row gap-8">

          {/* Left Column: Project Information Form */}
          <div className="flex-1 max-w-4xl">
            <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8">
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-white mb-3">Project Information</h3>
                <p className="text-slate-300 text-base">Complete all project details and requirements</p>
              </div>

            {/* Collapsible Categories */}
            <div className="space-y-6">

              {/* User Information Category - Moved to Top */}
              <CollapsibleCategory
                title="Your Information"
                category="userInfo"
                isCollapsed={collapsedCategories.userInfo}
                onToggle={() => toggleCategory('userInfo')}
                icon={
                  <svg className="w-5 h-5 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                }
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Company Name */}
                  <div>
                    <label className="block text-white font-semibold mb-2 text-sm">Your Company Name</label>
                    <input
                      type="text"
                      value={formData.userCompanyName}
                      onChange={(e) => setFormData(prev => ({ ...prev, userCompanyName: e.target.value }))}
                      placeholder="Your company name"
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300"
                    />
                  </div>

                  {/* Contact Name */}
                  <div>
                    <label className="block text-white font-semibold mb-2 text-sm">Your Name</label>
                    <input
                      type="text"
                      value={formData.userContactName}
                      onChange={(e) => setFormData(prev => ({ ...prev, userContactName: e.target.value }))}
                      placeholder="Your full name"
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300"
                    />
                  </div>

                  {/* Email */}
                  <div>
                    <label className="block text-white font-semibold mb-2 text-sm">Your Email</label>
                    <input
                      type="email"
                      value={formData.userEmail}
                      onChange={(e) => setFormData(prev => ({ ...prev, userEmail: e.target.value }))}
                      placeholder="<EMAIL>"
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300"
                    />
                  </div>

                  {/* Phone */}
                  <div>
                    <label className="block text-white font-semibold mb-2 text-sm">Your Phone</label>
                    <input
                      type="tel"
                      value={formData.userPhone}
                      onChange={(e) => setFormData(prev => ({ ...prev, userPhone: e.target.value }))}
                      placeholder="+****************"
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300"
                    />
                  </div>
                </div>

                {/* Title and Department */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Your Title */}
                  <div>
                    <label className="block text-white font-semibold mb-2 text-sm">Your Title</label>
                    {!showCustomInputs.userTitle ? (
                      <select
                        value={formData.userTitle}
                        onChange={(e) => handleDropdownChange('userTitle', e.target.value)}
                        className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:border-blue-400 focus:outline-none transition-all duration-300"
                      >
                        <option value="">Select your title</option>
                        {autoSuggestions.clientTitles.map((title, index) => (
                          <option key={index} value={title} className="bg-slate-800">{title}</option>
                        ))}
                        <option value="custom" className="bg-slate-800 text-blue-300">✏️ Custom Title</option>
                      </select>
                    ) : (
                      <div className="space-y-2">
                        <input
                          type="text"
                          value={customValues.userTitle}
                          onChange={(e) => setCustomValues(prev => ({ ...prev, userTitle: e.target.value }))}
                          placeholder="Enter custom title"
                          className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300"
                          autoFocus
                        />
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleCustomInputSubmit('userTitle')}
                            className="px-3 py-1 bg-green-500/20 border border-green-400/30 text-green-300 rounded-lg hover:bg-green-500/30 transition-all duration-300 text-sm"
                          >
                            ✓ Add
                          </button>
                          <button
                            onClick={() => handleCustomInputCancel('userTitle')}
                            className="px-3 py-1 bg-red-500/20 border border-red-400/30 text-red-300 rounded-lg hover:bg-red-500/30 transition-all duration-300 text-sm"
                          >
                            ✕ Cancel
                          </button>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Your Department */}
                  <div>
                    <label className="block text-white font-semibold mb-2 text-sm">Your Department</label>
                    {!showCustomInputs.userDepartment ? (
                      <select
                        value={formData.userDepartment}
                        onChange={(e) => handleDropdownChange('userDepartment', e.target.value)}
                        className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:border-blue-400 focus:outline-none transition-all duration-300"
                      >
                        <option value="">Select your department</option>
                        {autoSuggestions.departments.map((dept, index) => (
                          <option key={index} value={dept} className="bg-slate-800">{dept}</option>
                        ))}
                        <option value="custom" className="bg-slate-800 text-blue-300">✏️ Custom Department</option>
                      </select>
                    ) : (
                      <div className="space-y-2">
                        <input
                          type="text"
                          value={customValues.userDepartment}
                          onChange={(e) => setCustomValues(prev => ({ ...prev, userDepartment: e.target.value }))}
                          placeholder="Enter custom department"
                          className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300"
                          autoFocus
                        />
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleCustomInputSubmit('userDepartment')}
                            className="px-3 py-1 bg-green-500/20 border border-green-400/30 text-green-300 rounded-lg hover:bg-green-500/30 transition-all duration-300 text-sm"
                          >
                            ✓ Add
                          </button>
                          <button
                            onClick={() => handleCustomInputCancel('userDepartment')}
                            className="px-3 py-1 bg-red-500/20 border border-red-400/30 text-red-300 rounded-lg hover:bg-red-500/30 transition-all duration-300 text-sm"
                          >
                            ✕ Cancel
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Address - Full Width */}
                <div className="relative" style={{ zIndex: 10000 }}>
                  <label className="block text-white font-semibold mb-2 text-sm">Your Address</label>
                  <textarea
                    value={formData.userAddress}
                    onChange={(e) => {
                      handleUserAddressChange(e.target.value);
                      autoResizeTextarea(e.target);
                    }}
                    onInput={(e) => autoResizeTextarea(e.target as HTMLTextAreaElement)}
                    onFocus={() => {
                      if (formData.userAddress.length >= 3) {
                        const suggestions = generateAddressSuggestions(formData.userAddress);
                        setUserAddressSuggestions(suggestions);
                        setShowUserAddressSuggestions(suggestions.length > 0);
                      }
                    }}
                    onBlur={() => {
                      setTimeout(() => setShowUserAddressSuggestions(false), 200);
                    }}
                    placeholder="Start typing your business address (e.g., 123 Main St)"
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300 resize-none overflow-hidden relative"
                    style={{ minHeight: '60px', zIndex: 10001 }}
                  />

                  {/* User Address Suggestions Dropdown */}
                  {showUserAddressSuggestions && userAddressSuggestions.length > 0 && (
                    <div className="absolute w-full mt-1 bg-slate-900/95 backdrop-blur-md border border-slate-500 rounded-lg shadow-2xl max-h-48 overflow-y-auto"
                         style={{
                           zIndex: 10002,
                           position: 'absolute',
                           top: '100%',
                           left: 0,
                           right: 0
                         }}
                         onMouseDown={(e) => {
                           e.preventDefault();
                           e.stopPropagation();
                         }}
                         onClick={(e) => e.stopPropagation()}>
                      {userAddressSuggestions.map((suggestion, index) => (
                        <button
                          key={index}
                          onClick={(e) => selectUserAddressSuggestion(suggestion, e)}
                          onMouseDown={(e) => e.preventDefault()}
                          className="w-full px-4 py-2 text-left text-sm text-white hover:bg-slate-600/80 transition-colors duration-200 border-b border-slate-500 last:border-b-0"
                        >
                          <div className="flex items-center space-x-2">
                            <svg className="w-4 h-4 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            <span>{suggestion}</span>
                          </div>
                        </button>
                      ))}
                    </div>
                  )}

                  <div className="mt-1 text-xs text-slate-400">
                    Smart address suggestions appear as you type
                  </div>
                </div>
              </CollapsibleCategory>

              {/* Client Information Category */}
              <CollapsibleCategory
                title="Client Information"
                category="clientInfo"
                isCollapsed={collapsedCategories.clientInfo}
                onToggle={() => toggleCategory('clientInfo')}
                icon={
                  <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                }
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Client Name */}
                  <div>
                    <label className="block text-white font-semibold mb-2 text-sm">Client Name</label>
                    <input
                      type="text"
                      value={formData.clientName}
                      onChange={(e) => setFormData(prev => ({ ...prev, clientName: e.target.value }))}
                      placeholder="Enter client name"
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300"
                    />
                  </div>

                  {/* Client Title */}
                  <div>
                    <label className="block text-white font-semibold mb-2 text-sm">Title</label>
                    {!showCustomInputs.clientTitle ? (
                      <select
                        value={formData.clientTitle}
                        onChange={(e) => handleDropdownChange('clientTitle', e.target.value)}
                        className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:border-blue-400 focus:outline-none transition-all duration-300"
                      >
                        <option value="">Select title</option>
                        {autoSuggestions.clientTitles.map((title, index) => (
                          <option key={index} value={title} className="bg-slate-800">{title}</option>
                        ))}
                        <option value="custom" className="bg-slate-800 text-blue-300">✏️ Custom Title</option>
                      </select>
                    ) : (
                      <div className="space-y-2">
                        <input
                          type="text"
                          value={customValues.clientTitle}
                          onChange={(e) => setCustomValues(prev => ({ ...prev, clientTitle: e.target.value }))}
                          placeholder="Enter custom title"
                          className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300"
                          autoFocus
                        />
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleCustomInputSubmit('clientTitle')}
                            className="px-3 py-1 bg-green-500/20 border border-green-400/30 text-green-300 rounded-lg hover:bg-green-500/30 transition-all duration-300 text-sm"
                          >
                            ✓ Add
                          </button>
                          <button
                            onClick={() => handleCustomInputCancel('clientTitle')}
                            className="px-3 py-1 bg-red-500/20 border border-red-400/30 text-red-300 rounded-lg hover:bg-red-500/30 transition-all duration-300 text-sm"
                          >
                            ✕ Cancel
                          </button>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Client Company */}
                  <div>
                    <label className="block text-white font-semibold mb-2 text-sm">Company</label>
                    <input
                      type="text"
                      value={formData.clientCompany}
                      onChange={(e) => setFormData(prev => ({ ...prev, clientCompany: e.target.value }))}
                      placeholder="Enter company name"
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300"
                    />
                  </div>

                  {/* Department */}
                  <div>
                    <label className="block text-white font-semibold mb-2 text-sm">Department</label>
                    {!showCustomInputs.clientDepartment ? (
                      <select
                        value={formData.clientDepartment}
                        onChange={(e) => handleDropdownChange('clientDepartment', e.target.value)}
                        className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:border-blue-400 focus:outline-none transition-all duration-300"
                      >
                        <option value="">Select department</option>
                        {autoSuggestions.departments.map((dept, index) => (
                          <option key={index} value={dept} className="bg-slate-800">{dept}</option>
                        ))}
                        <option value="custom" className="bg-slate-800 text-blue-300">✏️ Custom Department</option>
                      </select>
                    ) : (
                      <div className="space-y-2">
                        <input
                          type="text"
                          value={customValues.clientDepartment}
                          onChange={(e) => setCustomValues(prev => ({ ...prev, clientDepartment: e.target.value }))}
                          placeholder="Enter custom department"
                          className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300"
                          autoFocus
                        />
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleCustomInputSubmit('clientDepartment')}
                            className="px-3 py-1 bg-green-500/20 border border-green-400/30 text-green-300 rounded-lg hover:bg-green-500/30 transition-all duration-300 text-sm"
                          >
                            ✓ Add
                          </button>
                          <button
                            onClick={() => handleCustomInputCancel('clientDepartment')}
                            className="px-3 py-1 bg-red-500/20 border border-red-400/30 text-red-300 rounded-lg hover:bg-red-500/30 transition-all duration-300 text-sm"
                          >
                            ✕ Cancel
                          </button>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Client Email */}
                  <div>
                    <label className="block text-white font-semibold mb-2 text-sm">Email</label>
                    <input
                      type="email"
                      value={formData.clientEmail}
                      onChange={(e) => setFormData(prev => ({ ...prev, clientEmail: e.target.value }))}
                      placeholder="<EMAIL>"
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300"
                    />
                  </div>

                  {/* Client Phone */}
                  <div>
                    <label className="block text-white font-semibold mb-2 text-sm">Phone</label>
                    <input
                      type="tel"
                      value={formData.clientPhone}
                      onChange={(e) => setFormData(prev => ({ ...prev, clientPhone: e.target.value }))}
                      placeholder="+****************"
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300"
                    />
                  </div>
                </div>

                {/* Client Address - Full Width */}
                <div className="relative" style={{ zIndex: 10000 }}>
                  <label className="block text-white font-semibold mb-2 text-sm">Address</label>
                  <textarea
                    value={formData.clientAddress}
                    onChange={(e) => {
                      handleAddressChange(e.target.value);
                      autoResizeTextarea(e.target);
                    }}
                    onInput={(e) => autoResizeTextarea(e.target as HTMLTextAreaElement)}
                    onFocus={() => {
                      if (formData.clientAddress.length >= 3) {
                        const suggestions = generateAddressSuggestions(formData.clientAddress);
                        setAddressSuggestions(suggestions);
                        setShowAddressSuggestions(suggestions.length > 0);
                      }
                    }}
                    onBlur={() => {
                      setTimeout(() => setShowAddressSuggestions(false), 200);
                    }}
                    placeholder="Start typing address (e.g., 123 Main St)"
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300 resize-none overflow-hidden relative"
                    style={{ minHeight: '60px', zIndex: 10001 }}
                  />

                  {/* Address Suggestions Dropdown */}
                  {showAddressSuggestions && addressSuggestions.length > 0 && (
                    <div className="absolute w-full mt-1 bg-slate-900/95 backdrop-blur-md border border-slate-500 rounded-lg shadow-2xl max-h-48 overflow-y-auto"
                         style={{
                           zIndex: 10002,
                           position: 'absolute',
                           top: '100%',
                           left: 0,
                           right: 0
                         }}
                         onMouseDown={(e) => {
                           e.preventDefault();
                           e.stopPropagation();
                         }}
                         onClick={(e) => e.stopPropagation()}>
                      {addressSuggestions.map((suggestion, index) => (
                        <button
                          key={index}
                          onClick={(e) => selectAddressSuggestion(suggestion, e)}
                          onMouseDown={(e) => e.preventDefault()}
                          className="w-full px-4 py-2 text-left text-sm text-white hover:bg-slate-600/80 transition-colors duration-200 border-b border-slate-500 last:border-b-0"
                        >
                          <div className="flex items-center space-x-2">
                            <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            <span>{suggestion}</span>
                          </div>
                        </button>
                      ))}
                    </div>
                  )}

                  <div className="mt-1 text-xs text-slate-400">
                    Smart address suggestions appear as you type
                  </div>
                </div>
              </CollapsibleCategory>

              {/* Timeline Category */}
              <CollapsibleCategory
                title="Timeline & Project"
                category="timeline"
                isCollapsed={collapsedCategories.timeline}
                onToggle={() => toggleCategory('timeline')}
                icon={
                  <svg className="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                }
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Project Name */}
                  <div>
                    <label className="block text-white font-semibold mb-2 text-sm">Project Name</label>
                    <input
                      type="text"
                      value={formData.projectName}
                      onChange={(e) => setFormData(prev => ({ ...prev, projectName: e.target.value }))}
                      placeholder="Enter project name"
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300"
                    />
                  </div>

                  {/* Project Type */}
                  <div>
                    <label className="block text-white font-semibold mb-2 text-sm">Project Type</label>
                    {!showCustomInputs.projectType ? (
                      <select
                        value={formData.projectType}
                        onChange={(e) => handleDropdownChange('projectType', e.target.value)}
                        className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:border-blue-400 focus:outline-none transition-all duration-300"
                      >
                        <option value="">Select project type</option>
                        {autoSuggestions.projectTypes.map((type, index) => (
                          <option key={index} value={type} className="bg-slate-800">{type}</option>
                        ))}
                        <option value="custom" className="bg-slate-800 text-blue-300">✏️ Custom Project Type</option>
                      </select>
                    ) : (
                      <div className="space-y-2">
                        <input
                          type="text"
                          value={customValues.projectType}
                          onChange={(e) => setCustomValues(prev => ({ ...prev, projectType: e.target.value }))}
                          placeholder="Enter custom project type"
                          className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300"
                          autoFocus
                        />
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleCustomInputSubmit('projectType')}
                            className="px-3 py-1 bg-green-500/20 border border-green-400/30 text-green-300 rounded-lg hover:bg-green-500/30 transition-all duration-300 text-sm"
                          >
                            ✓ Add
                          </button>
                          <button
                            onClick={() => handleCustomInputCancel('projectType')}
                            className="px-3 py-1 bg-red-500/20 border border-red-400/30 text-red-300 rounded-lg hover:bg-red-500/30 transition-all duration-300 text-sm"
                          >
                            ✕ Cancel
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Timeline Fields - Full Width */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Start Date */}
                  <div>
                    <label className="block text-white font-semibold mb-2 text-sm">Start Date</label>
                    <input
                      type="date"
                      value={formData.startDate}
                      onChange={(e) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:border-blue-400 focus:outline-none transition-all duration-300"
                    />
                  </div>

                  {/* End Date */}
                  <div>
                    <label className="block text-white font-semibold mb-2 text-sm">End Date</label>
                    <div className="space-y-2">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => setFormData(prev => ({ ...prev, endDate: 'TBD' }))}
                          className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                            formData.endDate === 'TBD'
                              ? 'bg-orange-500/20 border border-orange-400/50 text-orange-300'
                              : 'bg-white/10 border border-white/20 text-slate-300 hover:bg-white/20'
                          }`}
                        >
                          TBD
                        </button>
                        <input
                          type="date"
                          value={formData.endDate === 'TBD' ? '' : formData.endDate}
                          onChange={(e) => setFormData(prev => ({ ...prev, endDate: e.target.value }))}
                          className="flex-1 px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:border-blue-400 focus:outline-none transition-all duration-300 text-sm"
                          placeholder="Select end date"
                        />
                      </div>
                      {formData.endDate === 'TBD' && (
                        <div className="text-xs text-orange-300 bg-orange-500/10 border border-orange-400/20 rounded-lg px-3 py-2">
                          End date to be determined - Duration will show as TBD
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Duration - Auto-calculated */}
                <div>
                  <label className="block text-white font-semibold mb-2 text-sm">
                    Duration
                    <span className="ml-2 text-xs text-blue-300">(Auto-calculated)</span>
                  </label>
                  <div className="w-full px-3 py-2 bg-white/5 border border-white/10 rounded-lg text-white transition-all duration-300 flex items-center justify-between">
                    <span className={`text-sm ${formData.duration === 'TBD' || formData.duration === 'Invalid dates' ? 'text-slate-400' : 'text-green-300'}`}>
                      {formData.duration || 'Select start and end dates'}
                    </span>
                    <div className="flex items-center space-x-1 text-xs text-slate-400">
                      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                      </svg>
                      <span>Auto</span>
                    </div>
                  </div>
                  <div className="mt-1 text-xs text-slate-400">
                    Duration is automatically calculated from your start and end dates
                  </div>
                </div>
              </CollapsibleCategory>

              {/* Budget Category */}
              <CollapsibleCategory
                title="Budget & Payments"
                category="budget"
                isCollapsed={collapsedCategories.budget}
                onToggle={() => toggleCategory('budget')}
                icon={
                  <svg className="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                }
              >
                {/* Budget Section */}
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-white font-semibold mb-2 text-sm">Hourly Rate</label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={formData.hourlyRate}
                      onChange={(e) => {
                        const newRate = e.target.value;
                        setFormData(prev => ({ ...prev, hourlyRate: newRate }));
                        calculateBudget(formData.estimatedHours, newRate);
                      }}
                      placeholder="150"
                      className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300 text-sm"
                    />
                  </div>
                  <div>
                    <label className="block text-white font-semibold mb-2 text-sm">Est. Hours</label>
                    <input
                      type="number"
                      min="0"
                      step="0.5"
                      value={formData.estimatedHours}
                      onChange={(e) => {
                        const newHours = e.target.value;
                        setFormData(prev => ({ ...prev, estimatedHours: newHours }));
                        calculateBudget(newHours, formData.hourlyRate);
                      }}
                      placeholder="80"
                      className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300 text-sm"
                    />
                  </div>
                </div>

                {/* Total Budget */}
                <div>
                  <label className="block text-white font-semibold mb-2 text-sm">Total Budget</label>
                  <div className="relative">
                    <div className="w-full px-4 py-3 bg-green-500/10 border border-green-400/20 rounded-lg text-green-300 font-bold text-lg text-center">
                      ${formatBudget(formData.totalBudget)}
                    </div>
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-green-400">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                      </svg>
                    </div>
                  </div>
                  <p className="text-xs text-slate-400 mt-1">Auto-calculated from rate × hours</p>
                </div>

                {/* Payment Templates */}
                <div className="mb-4">
                  <h5 className="text-white font-semibold mb-2 text-sm">Payment Templates</h5>
                  <div className="space-y-1">
                    <button
                      onClick={() => autoDistributeBudget('50/50')}
                      className="w-full px-3 py-2 bg-purple-500/10 border border-purple-400/20 rounded-lg text-purple-300 hover:bg-purple-500/20 transition-all duration-300 text-sm text-left"
                    >
                      <div className="font-medium">50/50 Split</div>
                      <div className="text-xs text-purple-200/80">50% upfront + 50% completion</div>
                    </button>

                    <button
                      onClick={() => autoDistributeBudget('3-payment')}
                      className="w-full px-3 py-2 bg-purple-500/10 border border-purple-400/20 rounded-lg text-purple-300 hover:bg-purple-500/20 transition-all duration-300 text-sm text-left"
                    >
                      <div className="font-medium">3-Payment Plan</div>
                      <div className="text-xs text-purple-200/80">40% + 30% + 30%</div>
                    </button>

                    <button
                      onClick={() => autoDistributeBudget('monthly')}
                      className="w-full px-3 py-2 bg-purple-500/10 border border-purple-400/20 rounded-lg text-purple-300 hover:bg-purple-500/20 transition-all duration-300 text-sm text-left"
                    >
                      <div className="font-medium">Monthly Billing</div>
                      <div className="text-xs text-purple-200/80">25% × 4 payments</div>
                    </button>
                  </div>
                  <div className="mt-2 text-xs text-slate-400">
                    Auto-calculates from total budget: ${formatBudget(formData.totalBudget)}
                  </div>
                </div>

                {/* Custom Payment Schedule */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <h5 className="text-white font-semibold text-sm">Custom Payments</h5>
                    <button
                      onClick={addPaymentInstallment}
                      className="px-2 py-1 bg-green-500/20 border border-green-400/30 rounded text-green-300 hover:bg-green-500/30 transition-all duration-300 text-xs"
                    >
                      + Add
                    </button>
                  </div>

                  <div className="space-y-2">
                    {formData.paymentSchedule.map((installment, index) => (
                      <div key={installment.id} className="bg-black/20 rounded-lg p-2">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-white font-medium text-xs">#{index + 1}</span>
                          <button
                            onClick={() => removePaymentInstallment(installment.id)}
                            className="text-red-400 hover:text-red-300 text-xs"
                          >
                            ×
                          </button>
                        </div>

                        <div className="space-y-1">
                          <input
                            type="text"
                            value={installment.description}
                            onChange={(e) => updatePaymentInstallment(installment.id, 'description', e.target.value)}
                            placeholder="Payment description"
                            className="w-full px-2 py-1 bg-white/10 border border-white/20 rounded text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none text-xs"
                          />

                          <input
                            type="text"
                            value={installment.amount}
                            onChange={(e) => updatePaymentInstallment(installment.id, 'amount', e.target.value)}
                            placeholder="$0.00"
                            className="w-full px-2 py-1 bg-white/10 border border-white/20 rounded text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none text-xs"
                          />

                          <div className="flex space-x-1">
                            <button
                              onClick={() => updatePaymentInstallment(installment.id, 'dueDate', 'TBD')}
                              className={`px-2 py-1 rounded text-xs font-medium transition-all duration-300 ${
                                installment.dueDate === 'TBD'
                                  ? 'bg-orange-500/20 border border-orange-400/50 text-orange-300'
                                  : 'bg-white/10 border border-white/20 text-slate-300 hover:bg-white/20'
                              }`}
                            >
                              TBD
                            </button>
                            <input
                              type="date"
                              value={installment.dueDate === 'TBD' ? '' : installment.dueDate}
                              onChange={(e) => updatePaymentInstallment(installment.id, 'dueDate', e.target.value)}
                              className="flex-1 px-2 py-1 bg-white/10 border border-white/20 rounded text-white focus:border-blue-400 focus:outline-none text-xs"
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {formData.paymentSchedule.length === 0 && (
                    <div className="text-center py-3 text-slate-400 text-xs">
                      No payments set. Use templates above.
                    </div>
                  )}
                </div>
              </CollapsibleCategory>

              {/* Project Details Category */}
              <CollapsibleCategory
                title="Project Details & Scope"
                category="projectDetails"
                isCollapsed={collapsedCategories.projectDetails}
                onToggle={() => toggleCategory('projectDetails')}
                icon={
                  <svg className="w-5 h-5 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                }
              >
                {/* Project Description */}
                <div className="mb-6">
                  <label className="block text-white font-semibold mb-3 text-base">Project Description</label>
                  <textarea
                    value={formData.projectDescription}
                    onChange={(e) => {
                      setFormData(prev => ({ ...prev, projectDescription: e.target.value }));
                      autoResizeTextarea(e.target);
                    }}
                    onInput={(e) => autoResizeTextarea(e.target as HTMLTextAreaElement)}
                    placeholder="Describe the project scope, requirements, and any specific details..."
                    className="w-full px-5 py-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300 resize-none overflow-hidden text-base"
                    style={{ minHeight: '120px' }}
                  />
                </div>

                {/* Deliverables */}
                <div className="mb-6">
                  <label className="block text-white font-semibold mb-3 text-base">
                    Deliverables
                    {formData.deliverables.length > 0 && (
                      <span className="ml-2 text-sm text-blue-300">({formData.deliverables.length} selected)</span>
                    )}
                  </label>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {autoSuggestions.deliverables.map((deliverable, index) => (
                      <button
                        key={index}
                        onClick={() => {
                          if (formData.deliverables.includes(deliverable)) {
                            removeDeliverable(index);
                          } else {
                            addDeliverable(deliverable);
                          }
                        }}
                        className={`px-2 py-1 rounded-lg text-xs font-medium transition-all duration-300 border ${
                          formData.deliverables.includes(deliverable)
                            ? 'bg-blue-500/20 border-blue-400/50 text-blue-300 shadow-lg shadow-blue-500/20'
                            : 'bg-white/10 border-white/20 text-slate-300 hover:bg-white/20 hover:border-white/30'
                        }`}
                      >
                        {formData.deliverables.includes(deliverable) && (
                          <span className="mr-1">✓</span>
                        )}
                        {deliverable}
                      </button>
                    ))}
                  </div>

                  {/* Custom deliverable input */}
                  <div className="flex items-center space-x-3">
                    <input
                      type="text"
                      placeholder="Add custom deliverable"
                      className="flex-1 px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300 text-base"
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          const value = e.currentTarget.value.trim();
                          if (value && !formData.deliverables.includes(value)) {
                            addDeliverable(value);
                            e.currentTarget.value = '';
                          }
                        }
                      }}
                    />
                    <span className="text-sm text-slate-400 whitespace-nowrap">Press Enter to add</span>
                  </div>
                </div>

                {/* Additional Requirements */}
                <div className="mb-6">
                  <label className="block text-white font-semibold mb-3 text-base">Additional Requirements</label>
                  <textarea
                    value={formData.additionalRequirements || ''}
                    onChange={(e) => {
                      setFormData(prev => ({ ...prev, additionalRequirements: e.target.value }));
                      autoResizeTextarea(e.target);
                    }}
                    onInput={(e) => autoResizeTextarea(e.target as HTMLTextAreaElement)}
                    placeholder="Any special requirements, constraints, or additional details..."
                    className="w-full px-5 py-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300 resize-none overflow-hidden text-base"
                    style={{ minHeight: '100px' }}
                  />
                </div>

                {/* Enhanced Live Summary */}
                <div className="bg-blue-500/10 border border-blue-400/20 rounded-xl p-6 mb-6">
                  <h4 className="text-blue-300 font-semibold mb-4 text-base">Project Summary</h4>
                  <div className="text-blue-200 text-sm space-y-2">
                    <p><strong>Client:</strong> {formData.clientName || 'Not set'} {formData.clientTitle ? `(${formData.clientTitle})` : ''}</p>
                    <p><strong>Company:</strong> {formData.clientCompany || 'Not set'} {formData.clientDepartment ? `- ${formData.clientDepartment}` : ''}</p>
                    <p><strong>Contact:</strong> {formData.clientEmail || 'Not set'} {formData.clientPhone ? `| ${formData.clientPhone}` : ''}</p>
                    <p><strong>Project:</strong> {formData.projectName || 'Not set'} ({formData.projectType || 'Not set'})</p>
                    <p><strong>Timeline:</strong> {formData.startDate || 'Not set'} to {formData.endDate || 'Not set'}</p>
                    <p><strong>Budget:</strong> ${formatBudget(formData.totalBudget)} ({formatBudget(formData.estimatedHours)} hours @ ${formatBudget(formData.hourlyRate)}/hr)</p>

                    {formData.paymentSchedule.length > 0 && (
                      <p><strong>Schedule:</strong> {formData.paymentSchedule.length} installments</p>
                    )}
                    <p><strong>Deliverables:</strong> {formData.deliverables.length > 0 ? formData.deliverables.join(', ') : 'None selected'}</p>
                  </div>
                </div>


              </CollapsibleCategory>

              {/* Advanced Options - Separate Top-Level Category */}
              <CollapsibleCategory
                title="Advanced Options"
                icon={
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                }
                isCollapsed={collapsedCategories['advanced-options']}
                onToggle={() => toggleCategory('advanced-options')}
              >
                <div className="space-y-6">
                  {/* Save as Template */}
                  <div>
                    <div className="flex items-center justify-between mb-4">
                      <label className="block text-white font-semibold text-base">💾 Save as Template</label>
                    </div>
                    <div className="space-y-3">
                      <p className="text-slate-300 text-sm">
                        Save your current form data as a template for future use. This will store all your client information, project details, and user information.
                      </p>
                      <div className="flex space-x-3">
                        <input
                          type="text"
                          placeholder="Enter template name (e.g., Web Development SOW Template)"
                          className="flex-1 px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300 text-sm"
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              const templateName = e.currentTarget.value.trim();
                              if (templateName) {
                                saveTemplateWithFormData(templateName);
                                e.currentTarget.value = '';
                              }
                            }
                          }}
                        />
                        <button
                          onClick={() => {
                            const input = document.querySelector('input[placeholder*="template name"]') as HTMLInputElement;
                            const templateName = input?.value.trim();
                            if (templateName) {
                              saveTemplateWithFormData(templateName);
                              input.value = '';
                            } else {
                              alert('Please enter a template name');
                            }
                          }}
                          className="px-4 py-3 bg-green-500/20 border border-green-400/30 text-green-300 rounded-lg hover:bg-green-500/30 transition-all duration-300 text-sm font-medium whitespace-nowrap"
                        >
                          💾 Save Template
                        </button>
                      </div>
                      <div className="text-xs text-slate-400">
                        Templates include all form data: client info, project details, budget, payment schedule, and deliverables
                      </div>
                    </div>
                  </div>

                  {/* AI Prompt Customization */}
                  <div>
                    <div className="flex items-center justify-between mb-4">
                      <label className="block text-white font-semibold text-base">AI Prompt Customization</label>
                      <label className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={useCustomPrompt}
                          onChange={(e) => {
                            setUseCustomPrompt(e.target.checked);
                            if (e.target.checked && !customPrompt) {
                              setCustomPrompt(generatePromptPreview());
                            }
                          }}
                          className="w-4 h-4 bg-white/10 border border-white/30 rounded text-blue-600 focus:ring-blue-500 focus:ring-2"
                        />
                        <span className="text-sm text-slate-300">Use Custom Prompt</span>
                      </label>
                    </div>

                    {useCustomPrompt ? (
                      <div className="space-y-3">
                        <textarea
                          value={customPrompt}
                          onChange={(e) => setCustomPrompt(e.target.value)}
                          placeholder="Enter your custom AI prompt here..."
                          className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300 h-32 resize-none text-sm font-mono"
                        />
                        <div className="flex items-center justify-between text-xs text-slate-400">
                          <span>{customPrompt.length} characters</span>
                          <button
                            onClick={() => setCustomPrompt(generatePromptPreview())}
                            className="px-3 py-1 bg-blue-500/20 border border-blue-400/30 text-blue-300 rounded-lg hover:bg-blue-500/30 transition-all duration-300"
                          >
                            Reset to Auto-Generated
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        <div className="p-4 bg-slate-800/50 border border-slate-600/30 rounded-xl">
                          <div className="text-xs text-slate-400 mb-2">Current Auto-Generated Prompt Preview:</div>
                          <pre className="text-xs text-slate-300 whitespace-pre-wrap font-mono max-h-32 overflow-y-auto">
                            {generatePromptPreview() || 'Fill out the form to see the prompt preview...'}
                          </pre>
                        </div>
                        <div className="text-xs text-slate-400">
                          Enable &quot;Use Custom Prompt&quot; to edit this prompt manually
                        </div>
                      </div>
                    )}

                    {/* Current Effective Prompt Display */}
                    <div className="mt-4 p-4 bg-blue-500/10 border border-blue-400/20 rounded-xl">
                      <div className="text-xs text-blue-300 mb-2 font-semibold">
                        Current Prompt Being Used:
                      </div>
                      <pre className="text-xs text-blue-200 whitespace-pre-wrap font-mono max-h-32 overflow-y-auto">
                        {useCustomPrompt ? (customPrompt || 'No custom prompt entered yet...') : (generatePromptPreview() || 'Fill out the form to see the prompt preview...')}
                      </pre>
                      <div className="mt-2 text-xs text-blue-300/80">
                        This is the exact prompt that will be sent to Gemini AI
                      </div>
                    </div>
                  </div>
                </div>
              </CollapsibleCategory>

            </div>
            </div>
          </div>

          {/* Right Column: Smart Analysis Sidebar */}
          {templateAnalysis && (
            <div className="w-full lg:w-80 flex-shrink-0">
              <div className="bg-gradient-to-b from-purple-500/10 to-blue-500/10 backdrop-blur-xl border border-purple-400/30 rounded-3xl p-6 h-full flex flex-col">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-bold text-white flex items-center">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                    Smart Analysis
                    {isAnalyzing && <div className="ml-3 w-5 h-5 border-2 border-purple-400 border-t-transparent rounded-full animate-spin"></div>}
                  </h3>
                </div>

                {/* Summary Stats */}
                <div className="flex items-center justify-between mb-6 text-sm">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                    <span className="text-green-300">Complete ({completed.length})</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                    <span className="text-red-300">Missing ({missing.length})</span>
                  </div>
                </div>

                {/* Grouped Requirements by Category */}
                <div className="space-y-4 flex-1 overflow-y-auto">
                  {(() => {
                    // Group requirements by category - Your Information first
                    const groupedRequirements: Record<string, any[]> = {
                      'Your Information': [],
                      'Client Information': [],
                      'Timeline & Budget': [],
                      'Project Details': [],
                      'Deliverables & Requirements': [],
                      'Other': []
                    };

                    // Categorize all requirements
                    [...missing, ...completed].forEach(req => {
                      const title = req.title?.toLowerCase() || '';
                      const category = req.category?.toLowerCase() || '';

                      if (category.includes('client') || title.includes('client')) {
                        groupedRequirements['Client Information'].push(req);
                      } else if (category.includes('user') || title.includes('your') || title.includes('vendor') || title.includes('provider')) {
                        groupedRequirements['Your Information'].push(req);
                      } else if (category.includes('project') || title.includes('project') || title.includes('scope')) {
                        groupedRequirements['Project Details'].push(req);
                      } else if (category.includes('timeline') || category.includes('budget') || title.includes('date') || title.includes('timeline') || title.includes('budget') || title.includes('cost')) {
                        groupedRequirements['Timeline & Budget'].push(req);
                      } else if (category.includes('deliverable') || category.includes('requirement') || title.includes('deliverable') || title.includes('requirement')) {
                        groupedRequirements['Deliverables & Requirements'].push(req);
                      } else {
                        groupedRequirements['Other'].push(req);
                      }
                    });

                    return Object.entries(groupedRequirements).map(([categoryName, requirements]) => {
                      if (requirements.length === 0) return null;

                      const missingInCategory = requirements.filter(req => missing.includes(req));
                      const completedInCategory = requirements.filter(req => completed.includes(req));
                      const isCollapsed = (collapsedCategories as any)[`analysis_${categoryName.replace(/\s+/g, '')}`] || false;

                      return (
                        <div key={categoryName} className="border border-white/10 rounded-xl overflow-hidden">
                          {/* Category Header */}
                          <button
                            onClick={() => toggleCategory(`analysis_${categoryName.replace(/\s+/g, '')}`)}
                            className="w-full px-4 py-3 bg-white/5 hover:bg-white/10 transition-all duration-300 flex items-center justify-between text-left"
                          >
                            <div className="flex items-center space-x-3">
                              <div className={`w-2 h-2 rounded-full ${
                                missingInCategory.length > 0 ? 'bg-red-400' : 'bg-green-400'
                              }`}></div>
                              <span className="font-semibold text-white text-sm">{categoryName}</span>
                              <span className="text-xs text-slate-400">
                                ({completedInCategory.length}/{requirements.length})
                              </span>
                            </div>
                            <svg
                              className={`w-4 h-4 text-slate-400 transition-transform duration-300 ${
                                isCollapsed ? 'rotate-0' : 'rotate-180'
                              }`}
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                            </svg>
                          </button>

                          {/* Category Content */}
                          {!isCollapsed && (
                            <div className="p-3 space-y-2">
                              {/* Missing requirements first */}
                              {missingInCategory.map((field, index) => (
                                <div
                                  key={`missing-${field.id || index}`}
                                  className={`p-2 rounded-lg border transition-all duration-500 ${
                                    field.priority === 'high'
                                      ? 'bg-red-500/10 border-red-400/20'
                                      : 'bg-orange-500/10 border-orange-400/20'
                                  }`}
                                >
                                  <div className="flex items-center justify-between mb-1">
                                    <div className="w-4 h-4 rounded-full flex items-center justify-center bg-white/20">
                                      <div className={`w-1.5 h-1.5 rounded-full ${
                                        field.priority === 'high' ? 'bg-red-400' : 'bg-orange-400'
                                      }`}></div>
                                    </div>
                                    <span className={`px-1.5 py-0.5 rounded-full text-xs ${
                                      field.priority === 'high' ? 'bg-red-500/20 text-red-300' :
                                      field.priority === 'medium' ? 'bg-orange-500/20 text-orange-300' :
                                      'bg-blue-500/20 text-blue-300'
                                    }`}>
                                      {field.priority}
                                    </span>
                                  </div>
                                  <h5 className="font-medium text-xs mb-1 text-white">
                                    {field.title}
                                  </h5>
                                  <p className="text-xs text-slate-300 leading-relaxed">
                                    {field.description}
                                  </p>
                                </div>
                              ))}

                              {/* Completed requirements */}
                              {completedInCategory.map((field, index) => (
                                <div
                                  key={`completed-${field.id || index}`}
                                  className="p-2 rounded-lg border bg-green-500/5 border-green-400/20 opacity-60"
                                >
                                  <div className="flex items-center justify-between mb-1">
                                    <div className="w-4 h-4 rounded-full flex items-center justify-center bg-green-500">
                                      <svg className="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                      </svg>
                                    </div>
                                    <span className="px-1.5 py-0.5 rounded-full text-xs bg-green-500/20 text-green-300">
                                      Done
                                    </span>
                                  </div>
                                  <h5 className="font-medium text-xs mb-1 text-green-300">
                                    {field.title}
                                  </h5>
                                  <p className="text-xs text-green-200/60">
                                    Completed
                                  </p>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      );
                    }).filter(Boolean);
                  })()}

                  {/* Show message if all completed */}
                  {missing.length === 0 && completed.length > 0 && (
                    <div className="p-4 rounded-xl border bg-green-500/10 border-green-400/30 text-center">
                      <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <h4 className="font-semibold text-sm text-green-300 mb-1">
                        All Set!
                      </h4>
                      <p className="text-xs text-green-200/80">All fields completed</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

        </div>

        {/* Action Buttons */}
        <div className="text-center mt-6">
          <button
            onClick={handleClientInfoSubmit}
            className="px-12 py-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-2xl hover:from-green-700 hover:to-emerald-700 font-bold transition-all duration-300 flex items-center mx-auto text-lg"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
            Generate SOW with AI →
          </button>

          <div className="mt-4 flex justify-center">
            <button
              onClick={() => setStep('upload')}
              className="px-6 py-2 bg-white/10 border border-white/20 text-white rounded-xl hover:bg-white/20 font-medium transition-all duration-300 text-sm"
            >
              ← Back to Upload
            </button>
          </div>

          <p className="text-slate-400 text-sm mt-4">
            Required fields: Client Name, Company Name, Project Name, Project Description
          </p>
        </div>
      </div>
    );
  };

  const renderProcessingStep = () => (
    <div className="text-center">
      <LoadingSpinner size="lg" color="white" />
      <h2 className="text-3xl font-bold text-white mt-6 mb-2">Processing with AI</h2>
      <p className="text-slate-300">Gemini AI is customizing your SOW with the client information...</p>
    </div>
  );

  const renderReviewStep = () => (
    <div className="max-w-6xl mx-auto">
      <div className="text-center mb-6">
        <h2 className="text-4xl md:text-5xl font-black text-white mb-4">Information Review</h2>
        <p className="text-xl text-slate-300 max-w-3xl mx-auto leading-relaxed">
          Let&apos;s make sure we have everything needed for a perfect SOW
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">

        {/* Left Column: Initial Content Preview */}
        <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-6">
          <div className="text-center mb-6">
            <h3 className="text-xl font-bold text-white mb-2">Initial SOW Preview</h3>
            <p className="text-slate-300 text-sm">Based on your current information</p>
          </div>

          <div className="bg-black/20 rounded-xl p-4 max-h-64 overflow-y-auto">
            <div className="text-white text-sm leading-relaxed whitespace-pre-wrap">
              {reviewContent || 'Generating preview...'}
            </div>
          </div>
        </div>

        {/* Right Column: Missing Information Checklist */}
        <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-6">
          <div className="text-center mb-6">
            <h3 className="text-xl font-bold text-white mb-2">Information Checklist</h3>
            <p className="text-slate-300 text-sm">Complete these items for a perfect SOW</p>

            {/* Progress Bar */}
            <div className="mt-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-slate-400">Completion</span>
                <span className="text-sm font-bold text-white">{calculateCompletionPercentage()}%</span>
              </div>
              <div className="w-full bg-white/10 rounded-full h-2">
                <div
                  className="bg-gradient-to-r from-green-500 to-emerald-500 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${calculateCompletionPercentage()}%` }}
                ></div>
              </div>
            </div>
          </div>

          {missingInfo.length > 0 ? (
            <div className="space-y-4">
              {missingInfo.map((item) => (
                <div
                  key={item.id}
                  className={`p-4 rounded-xl border transition-all duration-300 ${
                    item.completed
                      ? 'bg-green-500/10 border-green-400/30'
                      : item.priority === 'high'
                        ? 'bg-red-500/10 border-red-400/20'
                        : item.priority === 'medium'
                          ? 'bg-orange-500/10 border-orange-400/20'
                          : 'bg-blue-500/10 border-blue-400/20'
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    {/* Interactive Checkbox */}
                    <button
                      onClick={() => toggleMissingInfoItem(item.id)}
                      className={`w-6 h-6 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5 transition-all duration-300 ${
                        item.completed
                          ? 'bg-green-500 text-white'
                          : 'bg-white/10 border border-white/30 hover:bg-white/20'
                      }`}
                    >
                      {item.completed && (
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      )}
                    </button>

                    {/* Content */}
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h4 className={`font-semibold ${item.completed ? 'text-green-300' : 'text-white'}`}>
                          {item.title}
                        </h4>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.priority === 'high'
                            ? 'bg-red-500/20 text-red-300'
                            : item.priority === 'medium'
                              ? 'bg-orange-500/20 text-orange-300'
                              : 'bg-blue-500/20 text-blue-300'
                        }`}>
                          {item.priority}
                        </span>
                        <span className="px-2 py-1 bg-white/10 rounded-full text-xs text-slate-300">
                          {item.category}
                        </span>
                      </div>

                      <p className={`text-sm mb-2 ${item.completed ? 'text-green-200' : 'text-slate-300'}`}>
                        {item.description}
                      </p>

                      {item.examples && (
                        <div className="text-xs text-slate-400 bg-black/20 rounded-lg p-2">
                          <span className="font-medium">Examples: </span>
                          {item.examples}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="mb-4">
                <svg className="w-16 h-16 text-green-400 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <p className="text-green-300 font-semibold">All information looks complete!</p>
              <p className="text-slate-400 text-sm">Ready to generate your SOW</p>
            </div>
          )}
        </div>
      </div>

      {/* Additional Information Input */}
      <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-6 mb-8">
        <div className="text-center mb-6">
          <h3 className="text-xl font-bold text-white mb-2">➕ Additional Information</h3>
          <p className="text-slate-300 text-sm">Add any missing details or clarifications</p>
        </div>

        <textarea
          value={additionalInfo}
          onChange={(e) => setAdditionalInfo(e.target.value)}
          placeholder="Add any additional project details, requirements, or clarifications that would help create a better SOW..."
          className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300 h-32 resize-none"
        />

        <div className="flex items-center justify-between mt-4">
          <div className="text-sm text-slate-400">
            {additionalInfo.length} characters
          </div>
          <div className="text-sm text-slate-400">
            Tip: Be specific about timelines, deliverables, and requirements
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between items-center">
        <button
          onClick={() => setStep('info')}
          className="px-8 py-4 bg-white/10 border border-white/20 text-white rounded-2xl hover:bg-white/20 font-bold transition-all duration-300 flex items-center"
        >
          ← Back to Form
        </button>

        <div className="flex space-x-4">
          {/* Completion Status */}
          <div className="flex items-center space-x-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-white">{calculateCompletionPercentage()}%</div>
              <div className="text-xs text-slate-400">Complete</div>
            </div>

            {missingInfo.length > 0 && (
              <div className="text-center">
                <div className="text-lg font-bold text-orange-300">
                  {missingInfo.filter(item => !item.completed).length}
                </div>
                <div className="text-xs text-slate-400">Remaining</div>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-4">
            {missingInfo.length > 0 && missingInfo.some(item => !item.completed) && (
              <button
                onClick={handleFinalProcessing}
                className="px-8 py-4 bg-orange-600 hover:bg-orange-700 text-white rounded-2xl font-bold transition-all duration-300 flex items-center"
              >
                Generate Anyway ({missingInfo.filter(item => !item.completed).length} missing)
              </button>
            )}

            <button
              onClick={handleFinalProcessing}
              className={`px-8 py-4 text-white rounded-2xl font-bold transition-all duration-300 flex items-center ${
                calculateCompletionPercentage() === 100
                  ? 'bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700'
                  : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700'
              }`}
            >
              {calculateCompletionPercentage() === 100
                ? 'Generate Perfect SOW →'
                : 'Generate SOW →'
              }
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderMissingInfoStep = () => {
    const currentItem = getCurrentMissingInfoItem();
    const incompleteItems = missingInfo.filter(item => !item.completed);
    const processedItems = getProcessedItems();
    const currentStepNumber = currentMissingInfoStep + 1;
    const totalSteps = incompleteItems.length;
    const progressPercentage = (processedItems.length / missingInfo.length) * 100;

    if (!currentItem) {
      return (
        <div className="max-w-4xl mx-auto text-center">
          <div className="mb-4">
            <svg className="w-16 h-16 text-green-400 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h2 className="text-4xl font-black text-white mb-4">All Information Collected!</h2>
          <p className="text-xl text-slate-300 mb-8">Ready to generate your perfect SOW</p>
          <button
            onClick={handleFinalProcessing}
            className="px-12 py-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-2xl hover:from-green-700 hover:to-emerald-700 font-bold transition-all duration-300 text-lg"
          >
            Generate Perfect SOW
          </button>
        </div>
      );
    }

    return (
      <div className="max-w-7xl mx-auto">
        {/* Progress Header */}
        <div className="text-center mb-6">
          <div className="flex items-center justify-center space-x-4 mb-6">
            <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
            </svg>
            <div>
              <h2 className="text-3xl md:text-4xl font-black text-white">Complete Your SOW</h2>
              <p className="text-lg text-slate-300">Step {currentStepNumber} of {totalSteps}</p>
            </div>
          </div>

          {/* Enhanced Progress Bar */}
          <div className="max-w-lg mx-auto">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-slate-400">Overall Progress</span>
              <div className="flex items-center space-x-4">
                <span className="text-sm text-slate-400">
                  {processedItems.length} of {missingInfo.length} completed
                </span>
                <span className="text-sm font-bold text-white">{Math.round(progressPercentage)}%</span>
              </div>
            </div>
            <div className="w-full bg-white/10 rounded-full h-3">
              <div
                className="bg-gradient-to-r from-blue-500 to-purple-500 h-3 rounded-full transition-all duration-700 ease-out"
                style={{ width: `${progressPercentage}%` }}
              ></div>
            </div>

            {/* Step Indicators */}
            <div className="flex justify-center mt-4 space-x-2">
              {missingInfo.map((item, index) => {
                const unprocessedItems = missingInfo.filter(i => !i.completed);
                const currentItem = unprocessedItems[currentMissingInfoStep];
                const isCurrentItem = currentItem && item.id === currentItem.id;

                return (
                  <div
                    key={item.id}
                    className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      item.completed
                        ? item.skipped
                          ? 'bg-yellow-500'
                          : 'bg-green-500'
                        : isCurrentItem
                          ? 'bg-blue-500 ring-2 ring-blue-300'
                          : item.userInput?.trim()
                            ? 'bg-orange-400'  // Has input but not completed
                            : 'bg-white/20'
                    }`}
                    title={
                      item.completed
                        ? (item.skipped ? 'Skipped' : 'Completed')
                        : isCurrentItem
                          ? 'Current'
                          : item.userInput?.trim()
                            ? 'Has Input'
                            : 'Pending'
                    }
                  />
                );
              })}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">

          {/* Left Column: Information Input */}
          <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8">
            <div className="mb-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className={`w-12 h-12 rounded-2xl flex items-center justify-center text-2xl ${
                  currentItem.priority === 'high'
                    ? 'bg-red-500/20 text-red-300'
                    : currentItem.priority === 'medium'
                      ? 'bg-orange-500/20 text-orange-300'
                      : 'bg-blue-500/20 text-blue-300'
                }`}>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    {currentItem.priority === 'high' ? (
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    ) : currentItem.priority === 'medium' ? (
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    ) : (
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    )}
                  </svg>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white">{currentItem.title}</h3>
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                    currentItem.priority === 'high'
                      ? 'bg-red-500/20 text-red-300'
                      : currentItem.priority === 'medium'
                        ? 'bg-orange-500/20 text-orange-300'
                        : 'bg-blue-500/20 text-blue-300'
                  }`}>
                    {currentItem.priority} priority • {currentItem.category}
                  </span>
                </div>
              </div>

              <p className="text-slate-300 text-lg leading-relaxed mb-6">
                {currentItem.description}
              </p>

              {currentItem.examples && (
                <div className="bg-blue-500/10 border border-blue-400/20 rounded-xl p-4 mb-6">
                  <h4 className="text-blue-300 font-semibold mb-2 text-sm">Examples:</h4>
                  <p className="text-blue-200 text-sm">{currentItem.examples}</p>
                </div>
              )}
            </div>

            <div className="space-y-4">
              <label className="block text-white font-bold text-lg">
                Please provide this information:
              </label>

              {/* Helpful Instructions */}
              <div className="bg-blue-500/10 border border-blue-400/20 rounded-xl p-3 mb-4">
                <div className="flex items-center space-x-2 mb-2">
                  <svg className="w-5 h-5 text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="text-blue-300 font-semibold text-sm">Instructions</span>
                </div>
                <p className="text-blue-200 text-sm">
                  Fill in the details below, or click &quot;Skip This Field&quot; if you don&apos;t have this information.
                  You can always go back to previous steps using the &quot;← Previous Step&quot; button.
                </p>
              </div>

              <textarea
                value={currentItem.userInput || ''}
                onChange={(e) => updateMissingInfoInput(currentItem.id, e.target.value)}
                placeholder={`Enter ${currentItem.title.toLowerCase()}...`}
                className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none transition-all duration-300 h-32 resize-none text-lg"
                autoFocus
              />

              <div className="flex items-center justify-between">
                <div className="text-sm text-slate-400">
                  {(currentItem.userInput || '').length} characters
                </div>
                <div className="text-sm text-slate-400">
                  Be specific and detailed for best results
                </div>
              </div>
            </div>
          </div>

          {/* Right Column: Live SOW Preview */}
          <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8">
            <div className="text-center mb-6">
              <h3 className="text-2xl font-bold text-white mb-2">Live SOW Preview</h3>
              <p className="text-slate-300 text-sm">Updates as you add information</p>
            </div>

            <div className="bg-black/20 rounded-xl p-6 max-h-96 overflow-y-auto">
              <div className="text-white text-sm leading-relaxed whitespace-pre-wrap">
                {liveSOWPreview || 'Your SOW preview will appear here as you add information...'}
              </div>
            </div>

            {/* Completion Status */}
            <div className="mt-6 p-4 bg-green-500/10 border border-green-400/20 rounded-xl">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-green-300 font-semibold">Information Collected</div>
                  <div className="text-green-200 text-sm">
                    {missingInfo.filter(item => item.completed).length} of {missingInfo.length} items
                  </div>
                </div>
                <div className="text-3xl font-bold text-green-300">
                  {Math.round(calculateCompletionPercentage())}%
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Action Buttons */}
        <div className="flex justify-between items-center mt-8">
          {/* Left Side - Back Navigation */}
          <button
            onClick={handlePreviousMissingInfo}
            className="px-8 py-4 bg-white/10 border border-white/20 text-white rounded-2xl hover:bg-white/20 font-bold transition-all duration-300 flex items-center space-x-2"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            <span>{currentMissingInfoStep > 0 ? 'Previous Step' : 'Back to Form'}</span>
          </button>

          {/* Right Side - Action Buttons */}
          <div className="flex space-x-4">
            {/* Skip This Field Button */}
            <button
              onClick={skipCurrentMissingInfo}
              className="px-6 py-4 bg-yellow-600/20 border border-yellow-500/30 text-yellow-300 rounded-2xl hover:bg-yellow-600/30 font-bold transition-all duration-300 flex items-center space-x-2"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 9l3 3m0 0l-3 3m3-3H8m13 0a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>Skip This Field</span>
            </button>

            {/* Skip All & Generate Button */}
            <button
              onClick={() => handleFinalProcessing()}
              className="px-6 py-4 bg-orange-600/20 border border-orange-500/30 text-orange-300 rounded-2xl hover:bg-orange-600/30 font-bold transition-all duration-300 flex items-center space-x-2"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 9l3 3m0 0l-3 3m3-3H8" />
              </svg>
              <span>Skip All & Generate</span>
            </button>

            {/* Next Step Button */}
            <button
              onClick={handleNextMissingInfo}
              disabled={!currentItem.userInput?.trim()}
              className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-2xl hover:from-blue-700 hover:to-purple-700 font-bold transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              <span>
                {currentStepNumber === totalSteps ? 'Complete & Generate' : 'Next Step'}
              </span>
              {currentStepNumber < totalSteps && (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>
    );
  };

  const renderPreviewStep = () => (
    <div className="max-w-[1800px] mx-auto">
      <div className="text-center mb-6">
        <h2 className="text-3xl font-bold text-white mb-4">Your Customized SOW</h2>
        <p className="text-slate-300">Review and download your professional Statement of Work</p>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-5 gap-8">
        <div className="xl:col-span-4">
          {generatedDocxBuffer ? (
            <div
              id="sow-generator-docx-preview-container"
              className="docx-preview-container overflow-auto bg-slate-800/50 backdrop-blur-xl border border-white/20 rounded-2xl shadow-2xl"
              style={{
                minHeight: '900px',
                height: '900px',
                padding: '24px'
              }}
            >
              <p className="text-slate-300 text-sm mb-4">Loading final document preview...</p>
            </div>
          ) : (
            <div className="bg-slate-800/50 backdrop-blur-xl border border-white/20 rounded-2xl p-8 shadow-2xl h-[900px] overflow-auto">
              <div className="text-center py-8">
                <div className="text-slate-400 mb-4">
                  <svg className="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">Generating Preview...</h3>
                <p className="text-slate-300">Creating your customized DOCX preview</p>
              </div>
              <div className="mt-6 border-t border-white/20 pt-6">
                <h4 className="font-semibold text-white mb-2">Raw Content:</h4>
                <pre className="whitespace-pre-wrap font-serif text-sm leading-relaxed text-slate-300">
                  {processedMarkdown}
                </pre>
              </div>
            </div>
          )}
          {generatedDocxBuffer && <GeneratedDocxViewer docxBuffer={generatedDocxBuffer} />}
        </div>

        <div className="space-y-4">
          {/* SOW Name Editor */}
          <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl p-4">
            <h3 className="text-lg font-bold text-white mb-3">SOW Name</h3>
            <div className="space-y-3">
              <div>
                <label className="block text-slate-300 text-xs mb-2">Custom SOW Name (Optional)</label>
                <input
                  type="text"
                  value={customSOWName}
                  onChange={(e) => setCustomSOWName(e.target.value)}
                  className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 text-sm"
                  placeholder="e.g., Acme Corp Website Redesign SOW"
                />
                <p className="text-xs text-slate-400 mt-1">
                  Leave blank to auto-generate from client and project info
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl p-4">
            <h3 className="text-lg font-bold text-white mb-3">Actions</h3>
            <div className="space-y-3">
              <button
                onClick={async () => {
                  await handleDownload();
                  // Auto-save with custom name after download
                  await autoSaveSOW(customSOWName);
                }}
                disabled={isProcessing}
                className="w-full px-4 py-2.5 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 font-bold transition-all duration-300 disabled:opacity-50 text-sm"
              >
                Download & Save to Dashboard
              </button>
              <button
                onClick={() => setStep('info')}
                className="w-full px-4 py-2.5 bg-white/10 border border-white/20 text-white rounded-lg hover:bg-white/20 font-bold transition-all duration-300 text-sm"
              >
                ← Edit Information
              </button>
              <button
                onClick={() => {
                  setStep('upload');
                  setTemplate(null);
                  setProcessedMarkdown('');
                  setGeneratedDocxBuffer(null);
                  setCustomSOWName('');
                }}
                className="w-full px-4 py-2.5 bg-white/10 border border-white/20 text-white rounded-lg hover:bg-white/20 font-bold transition-all duration-300 text-sm"
              >
                🔄 Start Over
              </button>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl p-4">
            <h3 className="text-lg font-bold text-white mb-3">Project Information</h3>
            <div className="space-y-2 text-xs">
              <div>
                <span className="text-slate-400 block mb-1">Generated Prompt:</span>
                <div className="text-white bg-white/5 p-2 rounded-lg text-xs leading-relaxed max-h-24 overflow-y-auto">
                  {buildSmartPrompt()}
                </div>
              </div>
              <div><span className="text-slate-400">Template:</span> <span className="text-white">{template?.name}</span></div>
              <div><span className="text-slate-400">Generated:</span> <span className="text-green-400">{new Date().toLocaleDateString()}</span></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // NEW WORKFLOW: DOCX Review Step
  const renderDocxReviewStep = () => (
    <div className="max-w-[1600px] mx-auto">
      <div className="text-center mb-6">
        <h2 className="text-3xl font-bold text-white mb-4">Document Review</h2>
        <p className="text-slate-300">Review your processed document and provide any missing information</p>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-4 gap-8">
        {/* DOCX Preview - Larger on xl screens */}
        <div className="xl:col-span-3">
          {currentDocxBuffer ? (
            <div
              id="docx-review-preview-container"
              className="docx-preview-container w-full overflow-auto bg-slate-800/50 backdrop-blur-xl border border-white/20 rounded-2xl shadow-2xl"
              style={{
                minHeight: '800px',
                height: '800px',
                padding: '24px'
              }}
            >
              <p className="text-slate-300 text-sm mb-4">Loading document preview...</p>
            </div>
          ) : (
            <div className="flex items-center justify-center h-[800px] bg-slate-800/50 backdrop-blur-xl border border-white/20 rounded-2xl shadow-2xl">
              <div className="text-center">
                <div className="text-slate-400 mb-4">
                  <svg className="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">Converting Document...</h3>
                <p className="text-slate-300">Please wait while we prepare your document</p>
              </div>
            </div>
          )}
          {currentDocxBuffer && <ReviewDocxViewer docxBuffer={currentDocxBuffer} />}
        </div>

        {/* Document Summary & Missing Info */}
        <div className="space-y-4">
          {/* Document Summary */}
          <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl p-4">
            <h3 className="text-lg font-bold text-white mb-3">Document Summary</h3>
            <p className="text-slate-300 text-xs leading-relaxed">
              {documentSummary || 'Analyzing document...'}
            </p>
            <div className="mt-3 p-2 bg-white/5 rounded-xl">
              <div className="flex items-center justify-between">
                <span className="text-slate-400 text-xs">Completion Score</span>
                <span className="text-white font-bold text-sm">{completionScore}%</span>
              </div>
              <div className="w-full bg-white/10 rounded-full h-1.5 mt-1">
                <div
                  className="bg-gradient-to-r from-green-500 to-emerald-500 h-1.5 rounded-full transition-all duration-500"
                  style={{ width: `${completionScore}%` }}
                ></div>
              </div>
            </div>
          </div>

          {/* Missing Information */}
          {missingInfo.length > 0 && (
            <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl p-4">
              <h3 className="text-lg font-bold text-white mb-3 flex items-center">
                <svg className="w-5 h-5 mr-2 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                Missing Information ({missingInfo.length})
              </h3>
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {missingInfo.map((item, index) => (
                  <div key={item.id} className="bg-white/5 border border-white/10 rounded-lg p-3">
                    <div className="flex items-start justify-between mb-1">
                      <h4 className="text-white font-semibold text-xs">{item.title}</h4>
                      <span className={`px-1.5 py-0.5 rounded-full text-xs font-bold ${
                        item.priority === 'high' ? 'bg-red-500/20 text-red-300' :
                        item.priority === 'medium' ? 'bg-yellow-500/20 text-yellow-300' :
                        'bg-blue-500/20 text-blue-300'
                      }`}>
                        {item.priority}
                      </span>
                    </div>
                    <p className="text-slate-400 text-xs mb-1">{item.description}</p>
                    {item.examples && (
                      <p className="text-slate-500 text-xs italic">Examples: {item.examples}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl p-4">
            <h3 className="text-lg font-bold text-white mb-3">Actions</h3>
            <div className="space-y-3">
              {missingInfo.length > 0 ? (
                <button
                  onClick={() => setStep('missing-info')}
                  className="w-full px-4 py-2.5 bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-lg hover:from-orange-700 hover:to-red-700 font-bold transition-all duration-300 text-sm flex items-center justify-center"
                >
                  Provide Missing Information ({missingInfo.length})
                </button>
              ) : null}

              <button
                onClick={() => {
                  // If no missing info, go directly to final processing
                  if (missingInfo.length === 0) {
                    handleUserUpdates();
                  } else {
                    setStep('missing-info');
                  }
                }}
                className="w-full px-4 py-2.5 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg hover:from-green-700 hover:to-emerald-700 font-bold transition-all duration-300 text-sm"
              >
                Continue with Current Document
              </button>

              <button
                onClick={() => setStep('info')}
                className="w-full px-4 py-2.5 bg-white/10 border border-white/20 text-white rounded-lg hover:bg-white/20 font-bold transition-all duration-300 text-sm"
              >
                ← Edit Input Information
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // NEW WORKFLOW: Final Processing Step
  const renderFinalProcessingStep = () => (
    <div className="max-w-4xl mx-auto text-center">
      <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-12">
        <div className="mb-8">
          <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-3xl">🔄</span>
          </div>
          <h2 className="text-3xl font-bold text-white mb-4">Final Processing</h2>
          <p className="text-slate-300 text-lg">
            Applying your updates and generating the final document...
          </p>
        </div>

        <div className="space-y-4">
          <LoadingSpinner size="lg" color="white" />
          <div className="text-slate-400 text-sm">
            <p>Processing updates with AI...</p>
            <p>Converting to final DOCX format...</p>
            <p>Applying formatting and validation...</p>
          </div>
        </div>
      </div>
    </div>
  );

  // Review DOCX Viewer Component (EXACT SAME PATTERN AS DASHBOARD)
  const ReviewDocxViewer = ({ docxBuffer }: { docxBuffer: ArrayBuffer }) => {
    console.log('🔄 REVIEW DOCX VIEWER: Component function called');
    console.log('Buffer provided:', !!docxBuffer);
    console.log('Buffer size:', docxBuffer ? docxBuffer.byteLength : 'N/A');

    const [isLoading, setIsLoading] = useState(() => {
        console.log('🔄 REVIEW DOCX VIEWER: useState isLoading initializer');
        return true;
    });
    const [error, setError] = useState<string | null>(() => {
        console.log('🔄 REVIEW DOCX VIEWER: useState error initializer');
        return null;
    });
    const containerId = 'docx-review-preview-container';

    console.log('🔄 REVIEW DOCX VIEWER: About to define useEffect');

    useEffect(() => {
        console.log('🔄 REVIEW DOCX VIEWER: useEffect triggered');
        console.log('Buffer available:', !!docxBuffer);

        const loadDocxPreview = async () => {
            try {
                setIsLoading(true);
                setError(null);

                console.log('🔄 REVIEW DOCX VIEWER: Starting loadDocxPreview');
                console.log('Buffer size:', docxBuffer.byteLength);

                // Import docx-preview dynamically (EXACT SAME AS DASHBOARD)
                const { renderAsync } = await import('docx-preview');
                console.log('✅ REVIEW DOCX VIEWER: docx-preview library loaded');

                // Use getElementById with static ID (same as dashboard)
                const container = document.getElementById(containerId);
                console.log('Using container from getElementById:', !!container);

                if (!container) {
                    console.error('Review container not found with ID:', containerId);
                    throw new Error('Review preview container not found');
                }

                if (!docxBuffer || docxBuffer.byteLength === 0) {
                    throw new Error('Invalid DOCX buffer');
                }

                console.log('🔄 REVIEW DOCX VIEWER: Starting renderAsync...');
                if (container && docxBuffer) {
                    await renderAsync(docxBuffer, container, undefined, {
                        className: 'docx-wrapper',
                        inWrapper: true,
                        ignoreWidth: false,
                        ignoreHeight: false,
                        ignoreFonts: false,
                        breakPages: true,
                        ignoreLastRenderedPageBreak: true,
                        experimental: false,
                        trimXmlDeclaration: true,
                        useBase64URL: false
                    });
                    console.log('✅ REVIEW DOCX VIEWER: DOCX preview rendered successfully!');
                }
            } catch (err) {
                console.error('❌ REVIEW DOCX VIEWER: Failed to load DOCX preview:', err);
                setError(err instanceof Error ? err.message : 'Failed to load preview');
            } finally {
                setIsLoading(false);
            }
        };

        if (docxBuffer) {
            console.log('🔄 REVIEW DOCX VIEWER: Buffer available, starting load with retry mechanism...');

            // Retry mechanism to find the container (SAME AS DASHBOARD)
            let attempts = 0;
            const maxAttempts = 20;

            const tryLoadPreview = () => {
                attempts++;
                console.log(`🔄 REVIEW DOCX VIEWER: Attempt ${attempts}/${maxAttempts} to find container`);

                const container = document.getElementById(containerId);
                console.log(`Container found on attempt ${attempts}:`, !!container);

                // Debug: Show all elements with IDs on first attempt
                if (!container && attempts === 1) {
                    console.log('All elements with IDs:',
                        Array.from(document.querySelectorAll('[id]')).map(el => el.id));
                    console.log('All divs:',
                        Array.from(document.querySelectorAll('div')).length);
                }

                if (container) {
                    console.log('✅ REVIEW DOCX VIEWER: Container found, loading preview...');
                    loadDocxPreview();
                } else if (attempts < maxAttempts) {
                    console.log(`❌ REVIEW DOCX VIEWER: Container not found, retrying in 50ms...`);
                    setTimeout(tryLoadPreview, 50);
                } else {
                    console.error('❌ REVIEW DOCX VIEWER: Failed to find container after', maxAttempts, 'attempts');
                    setError('Container not found after multiple attempts');
                    setIsLoading(false);
                }
            };

            // Start the retry process
            setTimeout(tryLoadPreview, 10);
        } else {
            console.log('❌ REVIEW DOCX VIEWER: No buffer provided');
        }
    }, [docxBuffer]);



    if (isLoading) {
        return (
            <div className="bg-slate-800/30 p-4">
                <div className="text-sm text-slate-300 mb-2">
                    🔄 Review DOCX Viewer Component Active - Loading...
                </div>
                <div className="text-xs text-slate-400">
                    Looking for container: {containerId}
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="bg-slate-800/30 p-4">
                <div className="text-red-400 text-sm">
                    ❌ Review DOCX Viewer Error: {error}
                </div>
                <button
                    onClick={() => window.location.reload()}
                    className="mt-2 px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
                >
                    Retry Preview
                </button>
            </div>
        );
    }

    console.log('🔄 REVIEW DOCX VIEWER: Rendering component JSX');
    console.log('Container ID that will be used:', containerId);

    // Note: The actual container is created in the parent JSX, this component just handles the logic
    return (
        <div className="bg-slate-800/30 p-4">
            <div className="text-sm text-slate-300 mb-2">
                📄 Review DOCX Viewer Component Active
            </div>
            <div className="text-xs text-slate-400">
                Looking for container: {containerId}
            </div>
        </div>
    );
  };

  return (
    <div className="min-h-screen relative overflow-hidden">

      {/* Floating Elements */}
      <div className="absolute top-20 right-20 w-4 h-4 bg-blue-400/20 rounded-full animate-bounce delay-300"></div>
      <div className="absolute top-40 left-20 w-3 h-3 bg-purple-400/20 rounded-full animate-bounce delay-700"></div>
      <div className="absolute bottom-40 right-40 w-5 h-5 bg-pink-400/20 rounded-full animate-bounce delay-1000"></div>

      <div className="relative z-10 container mx-auto px-6 py-24">


        {/* Progress Steps */}
        <div className="flex justify-center mb-8 py-6">
          <div className="flex items-center space-x-8 px-8 py-4 bg-white/5 backdrop-blur-xl border border-white/10 rounded-3xl shadow-2xl">
            {[
              {
                key: 'upload',
                label: 'Upload',
                icon: (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                )
              },
              {
                key: 'info',
                label: 'Info',
                icon: (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                  </svg>
                )
              },
              {
                key: 'processing',
                label: 'Process',
                icon: (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                )
              },
              {
                key: 'docx-review',
                label: 'Review',
                icon: (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                )
              },
              {
                key: 'preview',
                label: 'Final',
                icon: (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                )
              }
            ].map((stepInfo, index) => {
              const stepOrder = ['upload', 'info', 'processing', 'docx-review', 'missing-info', 'final-processing', 'preview'];
              const currentStepIndex = stepOrder.indexOf(step);
              const isCompleted = currentStepIndex > index;
              const isCurrent = step === stepInfo.key ||
                (stepInfo.key === 'processing' && (step === 'processing' || step === 'final-processing')) ||
                (stepInfo.key === 'docx-review' && (step === 'docx-review' || step === 'missing-info'));

              return (
                <div key={stepInfo.key} className="flex items-center">
                  <div className={`relative w-16 h-16 rounded-2xl flex flex-col items-center justify-center font-bold text-xs transition-all duration-500 ${
                    isCurrent
                      ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-2xl shadow-purple-500/25 scale-110'
                      : isCompleted
                        ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-xl shadow-green-500/25'
                        : 'bg-white/10 text-slate-400 backdrop-blur-sm border border-white/20'
                  }`}>
                    <div className="text-lg">
                      {isCompleted ? (
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      ) : (
                        stepInfo.icon
                      )}
                    </div>
                    <div className="text-xs">{stepInfo.label}</div>
                    {isCurrent && (
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl blur-xl opacity-50 animate-pulse"></div>
                    )}
                  </div>
                  {index < 4 && (
                    <div className={`w-12 h-2 mx-3 rounded-full transition-all duration-500 ${
                      isCompleted
                        ? 'bg-gradient-to-r from-green-500 to-emerald-500'
                        : 'bg-white/20'
                    }`} />
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="max-w-2xl mx-auto mb-8">
            <div className="bg-red-500/10 border border-red-400/30 rounded-2xl p-6">
              <div className="flex items-start">
                <div className="text-red-400 mr-4">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-red-300 font-bold text-lg mb-2">Error</h3>
                  <p className="text-red-200">{error}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Step Content */}
        {isProcessing && step !== 'processing' ? (
          <div className="text-center">
            <LoadingSpinner size="lg" color="white" />
            <p className="text-slate-300 mt-4">Processing...</p>
          </div>
        ) : (
          <>
            {step === 'upload' && renderUploadStep()}
            {step === 'info' && renderInfoStep()}
            {step === 'processing' && renderProcessingStep()}
            {step === 'docx-review' && renderDocxReviewStep()}
            {step === 'missing-info' && renderMissingInfoStep()}
            {step === 'final-processing' && renderFinalProcessingStep()}
            {step === 'preview' && renderPreviewStep()}
          </>
        )}
      </div>
    </div>
  );
};

// Generated DOCX Viewer Component using static ID (SAME FIX AS DASHBOARD)
const GeneratedDocxViewer = ({ docxBuffer }: { docxBuffer: ArrayBuffer }) => {
  console.log('🔄 SOW GENERATOR VIEWER: Component function called');
  console.log('Buffer provided:', !!docxBuffer);
  console.log('Buffer size:', docxBuffer ? docxBuffer.byteLength : 'N/A');

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const containerId = 'sow-generator-docx-preview-container';

  useEffect(() => {
    console.log('🔄 SOW GENERATOR VIEWER: useEffect triggered');
    console.log('Buffer available:', !!docxBuffer);

    const loadDocxPreview = async () => {
      try {
        setIsLoading(true);
        setError(null);

        console.log('🔄 SOW GENERATOR VIEWER: Starting loadDocxPreview');
        console.log('Buffer size:', docxBuffer.byteLength);

        // Import docx-preview dynamically (EXACT SAME AS TEMPLATES)
        const { renderAsync } = await import('docx-preview');
        console.log('✅ SOW GENERATOR VIEWER: docx-preview library loaded');

        // Use getElementById with static ID (same as dashboard fix)
        const container = document.getElementById(containerId);
        console.log('Using container from getElementById:', !!container);

        if (!container) {
          console.error('SOW Generator container not found with ID:', containerId);
          throw new Error('SOW Generator preview container not found');
        }

        if (!docxBuffer || docxBuffer.byteLength === 0) {
          throw new Error('Invalid DOCX buffer');
        }

        console.log('🔄 SOW GENERATOR VIEWER: Starting renderAsync...');
        if (container && docxBuffer) {
          await renderAsync(docxBuffer, container, undefined, {
            className: 'docx-wrapper',
            inWrapper: true,
            ignoreWidth: false,
            ignoreHeight: false,
            ignoreFonts: false,
            breakPages: true,
            ignoreLastRenderedPageBreak: true,
            experimental: false,
            trimXmlDeclaration: true,
            useBase64URL: false
          });
          console.log('✅ SOW GENERATOR VIEWER: DOCX preview rendered successfully!');
        }
      } catch (err) {
        console.error('❌ SOW GENERATOR VIEWER: Failed to load DOCX preview:', err);
        setError(err instanceof Error ? err.message : 'Failed to load preview');
      } finally {
        setIsLoading(false);
      }
    };

    if (docxBuffer) {
      console.log('🔄 SOW GENERATOR VIEWER: Buffer available, starting load with retry mechanism...');

      // Retry mechanism to find the container (same as dashboard)
      let attempts = 0;
      const maxAttempts = 20;

      const tryLoadPreview = () => {
        attempts++;
        console.log(`🔄 SOW GENERATOR VIEWER: Attempt ${attempts}/${maxAttempts} to find container`);

        const container = document.getElementById(containerId);
        console.log(`Container found on attempt ${attempts}:`, !!container);

        // Debug: Show all elements with IDs on first attempt
        if (!container && attempts === 1) {
          console.log('All elements with IDs:',
            Array.from(document.querySelectorAll('[id]')).map(el => el.id));
          console.log('All divs:',
            Array.from(document.querySelectorAll('div')).length);
        }

        if (container) {
          console.log('✅ SOW GENERATOR VIEWER: Container found, loading preview...');
          loadDocxPreview();
        } else if (attempts < maxAttempts) {
          console.log(`❌ SOW GENERATOR VIEWER: Container not found, retrying in 50ms...`);
          setTimeout(tryLoadPreview, 50);
        } else {
          console.error('❌ SOW GENERATOR VIEWER: Failed to find container after', maxAttempts, 'attempts');
          setError('Container not found after multiple attempts');
          setIsLoading(false);
        }
      };

      // Start the retry process
      setTimeout(tryLoadPreview, 10);
    } else {
      console.log('❌ SOW GENERATOR VIEWER: No buffer provided');
    }
  }, [docxBuffer]);



  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-96 bg-slate-800/30">
        <LoadingSpinner size="lg" color="white" text="Loading DOCX preview..." />
        <p className="text-sm text-slate-300 mt-4">Rendering your generated SOW...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-96 bg-red-500/10 text-red-400">
        <div className="text-center p-6">
          <div className="mb-4">
            <svg className="w-10 h-10 text-red-400 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <p className="text-lg font-semibold mb-2 text-white">Preview Failed</p>
          <p className="text-sm mb-4 text-slate-300">{error}</p>
          <p className="text-xs text-slate-400">
            The DOCX file was generated successfully. You can still download it.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
          >
            Retry Preview
          </button>
        </div>
      </div>
    );
  }

  console.log('🔄 SOW GENERATOR VIEWER: Rendering component JSX');
  console.log('Container ID that will be used:', containerId);

  // Note: The actual container is created in the parent JSX, this component just handles the logic
  return (
    <div className="bg-slate-800/30 p-4">
      <div className="text-sm text-slate-300 mb-2">
        SOW Generator DOCX Viewer Component Active
      </div>
      <div className="text-xs text-slate-400">
        Looking for container: {containerId}
      </div>
      {error && (
        <div className="text-red-400 text-sm mt-2">
          Error: {error}
        </div>
      )}
      {isLoading && (
        <div className="text-blue-400 text-sm mt-2">
          Loading DOCX preview...
        </div>
      )}
    </div>
  );
};

// DOCX Preview Modal Component
const DocxPreviewModal = ({ templateId, onClose }: { templateId: string; onClose: () => void }) => {
  console.log('🔄 DOCX PREVIEW MODAL: Rendering modal for template:', templateId);

  useEffect(() => {
    console.log('🔄 DOCX PREVIEW MODAL: Modal mounted, checking DOM...');
    const modalElement = document.querySelector('.fixed.inset-0.bg-black\\/80');
    console.log('🔍 DOCX PREVIEW MODAL: Modal element found:', !!modalElement);
  }, []);

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-slate-900/95 backdrop-blur-xl border border-white/20 rounded-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden shadow-2xl">
        <div className="flex items-center justify-between p-6 border-b border-white/10">
          <h3 className="text-xl font-bold text-white">Template Preview</h3>
          <button
            onClick={onClose}
            className="w-10 h-10 bg-red-500/20 border border-red-400/30 text-red-300 rounded-xl hover:bg-red-500/30 transition-all duration-300 flex items-center justify-center"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div className="p-6 overflow-auto max-h-[calc(90vh-120px)] bg-slate-800/50">
          <DocxViewer templateId={templateId} />
        </div>
      </div>
    </div>
  );
};

// DOCX Viewer Component using docx-preview
const DocxViewer = ({ templateId }: { templateId: string }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadDocxPreview = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Import docx-preview dynamically (client-side only)
        const { renderAsync } = await import('docx-preview');

        // FIXED: Fetch the DOCX file directly using the correct preview endpoint
        console.log('🔍 DOCX VIEWER: Fetching template from:', `/api/template/${templateId}/preview`);
        const response = await fetch(`/api/template/${templateId}/preview`);
        console.log('🔍 DOCX VIEWER: Response status:', response.status);

        if (!response.ok) {
          console.error('❌ DOCX VIEWER: Failed to fetch template:', response.status, response.statusText);

          // Handle case where saved templates don't have DOCX files
          if (response.status === 404) {
            const errorData = await response.json().catch(() => ({}));
            if (errorData.error?.includes('saved templates')) {
              throw new Error('DOCX preview not available for saved templates. Only uploaded templates support DOCX preview.');
            }
          }

          throw new Error(`Failed to load template: ${response.status} ${response.statusText}`);
        }

        const arrayBuffer = await response.arrayBuffer();
        console.log('🔍 DOCX VIEWER: ArrayBuffer size:', arrayBuffer.byteLength);

        if (arrayBuffer.byteLength === 0) {
          throw new Error('Received empty DOCX file');
        }

        // FIXED: Wait for container element to be rendered with retry mechanism
        const waitForContainer = async (retries = 20, delay = 200): Promise<HTMLElement> => {
          for (let i = 0; i < retries; i++) {
            const container = document.getElementById(`docx-container-${templateId}`);
            console.log(`🔍 DOCX VIEWER: Container search attempt ${i + 1}/${retries}, found:`, !!container);

            if (container) {
              console.log('✅ DOCX VIEWER: Container found successfully!');
              // Additional check to ensure container is visible and has dimensions
              const rect = container.getBoundingClientRect();
              console.log('📐 DOCX VIEWER: Container dimensions:', rect.width, 'x', rect.height);
              return container;
            }

            console.log(`⏳ DOCX VIEWER: Waiting ${delay}ms for container to render...`);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
          throw new Error(`Container element not found after ${retries} attempts: docx-container-${templateId}`);
        };

        const container = await waitForContainer();

        console.log('🔄 DOCX VIEWER: Starting renderAsync...');
        await renderAsync(arrayBuffer, container, undefined, {
          className: 'docx-wrapper',
          inWrapper: true,
          ignoreWidth: false,
          ignoreHeight: false,
          ignoreFonts: false,
          breakPages: true,
          ignoreLastRenderedPageBreak: true,
          experimental: false,
          trimXmlDeclaration: true,
          useBase64URL: false
        });
        console.log('✅ DOCX VIEWER: renderAsync completed successfully!');
      } catch (err) {
        console.error('Failed to load DOCX preview:', err);
        setError(err instanceof Error ? err.message : 'Failed to load preview');
      } finally {
        setIsLoading(false);
      }
    };

    // Add a longer delay to ensure the modal is fully rendered in DOM
    const timeoutId = setTimeout(() => {
      loadDocxPreview();
    }, 200);

    return () => clearTimeout(timeoutId);
  }, [templateId]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <LoadingSpinner size="lg" color="blue" text="Loading DOCX preview..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-96 text-red-400">
        <div className="text-center">
          <p className="text-lg font-semibold mb-2 text-white">Failed to load preview</p>
          <p className="text-sm">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div
      id={`docx-container-${templateId}`}
      className="docx-preview-container"
      style={{ minHeight: '500px' }}
    />
  );
};

export default SOWGeneratorPage;
